"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/html-url-attributes";
exports.ids = ["vendor-chunks/html-url-attributes"];
exports.modules = {

/***/ "(ssr)/../node_modules/html-url-attributes/lib/index.js":
/*!********************************************************!*\
  !*** ../node_modules/html-url-attributes/lib/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   urlAttributes: () => (/* binding */ urlAttributes)\n/* harmony export */ });\n/**\n * HTML URL properties.\n *\n * Each key is a property name and each value is a list of tag names it applies\n * to or `null` if it applies to all elements.\n *\n * @type {Record<string, Array<string> | null>}\n */\nconst urlAttributes = {\n  action: ['form'],\n  cite: ['blockquote', 'del', 'ins', 'q'],\n  data: ['object'],\n  formAction: ['button', 'input'],\n  href: ['a', 'area', 'base', 'link'],\n  icon: ['menuitem'],\n  itemId: null,\n  manifest: ['html'],\n  ping: ['a', 'area'],\n  poster: ['video'],\n  src: [\n    'audio',\n    'embed',\n    'iframe',\n    'img',\n    'input',\n    'script',\n    'source',\n    'track',\n    'video'\n  ]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2h0bWwtdXJsLWF0dHJpYnV0ZXMvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcc2VydmVyTjhOXFxNQ1BDbGllbnRcXG5vZGVfbW9kdWxlc1xcaHRtbC11cmwtYXR0cmlidXRlc1xcbGliXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEhUTUwgVVJMIHByb3BlcnRpZXMuXG4gKlxuICogRWFjaCBrZXkgaXMgYSBwcm9wZXJ0eSBuYW1lIGFuZCBlYWNoIHZhbHVlIGlzIGEgbGlzdCBvZiB0YWcgbmFtZXMgaXQgYXBwbGllc1xuICogdG8gb3IgYG51bGxgIGlmIGl0IGFwcGxpZXMgdG8gYWxsIGVsZW1lbnRzLlxuICpcbiAqIEB0eXBlIHtSZWNvcmQ8c3RyaW5nLCBBcnJheTxzdHJpbmc+IHwgbnVsbD59XG4gKi9cbmV4cG9ydCBjb25zdCB1cmxBdHRyaWJ1dGVzID0ge1xuICBhY3Rpb246IFsnZm9ybSddLFxuICBjaXRlOiBbJ2Jsb2NrcXVvdGUnLCAnZGVsJywgJ2lucycsICdxJ10sXG4gIGRhdGE6IFsnb2JqZWN0J10sXG4gIGZvcm1BY3Rpb246IFsnYnV0dG9uJywgJ2lucHV0J10sXG4gIGhyZWY6IFsnYScsICdhcmVhJywgJ2Jhc2UnLCAnbGluayddLFxuICBpY29uOiBbJ21lbnVpdGVtJ10sXG4gIGl0ZW1JZDogbnVsbCxcbiAgbWFuaWZlc3Q6IFsnaHRtbCddLFxuICBwaW5nOiBbJ2EnLCAnYXJlYSddLFxuICBwb3N0ZXI6IFsndmlkZW8nXSxcbiAgc3JjOiBbXG4gICAgJ2F1ZGlvJyxcbiAgICAnZW1iZWQnLFxuICAgICdpZnJhbWUnLFxuICAgICdpbWcnLFxuICAgICdpbnB1dCcsXG4gICAgJ3NjcmlwdCcsXG4gICAgJ3NvdXJjZScsXG4gICAgJ3RyYWNrJyxcbiAgICAndmlkZW8nXG4gIF1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/html-url-attributes/lib/index.js\n");

/***/ })

};
;