"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-highlight";
exports.ids = ["vendor-chunks/rehype-highlight"];
exports.modules = {

/***/ "(ssr)/../node_modules/rehype-highlight/lib/index.js":
/*!*****************************************************!*\
  !*** ../node_modules/rehype-highlight/lib/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeHighlight)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/../node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var lowlight__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lowlight */ \"(ssr)/../node_modules/lowlight/lib/common.js\");\n/* harmony import */ var lowlight__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lowlight */ \"(ssr)/../node_modules/lowlight/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/../node_modules/unist-util-visit/lib/index.js\");\n/**\n * @import {ElementContent, Element, Root} from 'hast'\n * @import {LanguageFn} from 'lowlight'\n * @import {VFile} from 'vfile'\n */\n\n/**\n * @typedef Options\n *   Configuration (optional).\n * @property {Readonly<Record<string, ReadonlyArray<string> | string>> | null | undefined} [aliases={}]\n *   Register more aliases (optional);\n *   passed to `lowlight.registerAlias`.\n * @property {boolean | null | undefined} [detect=false]\n *   Highlight code without language classes by guessing its programming\n *   language (default: `false`).\n * @property {Readonly<Record<string, LanguageFn>> | null | undefined} [languages]\n *   Register languages (default: `common`);\n *   passed to `lowlight.register`.\n * @property {ReadonlyArray<string> | null | undefined} [plainText=[]]\n *   List of language names to not highlight (optional);\n *   note you can also add `no-highlight` classes.\n * @property {string | null | undefined} [prefix='hljs-']\n *   Class prefix (default: `'hljs-'`).\n * @property {ReadonlyArray<string> | null | undefined} [subset]\n *   Names of languages to check when detecting (default: all registered\n *   languages).\n */\n\n\n\n\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Apply syntax highlighting.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns\n *   Transform.\n */\nfunction rehypeHighlight(options) {\n  const settings = options || emptyOptions\n  const aliases = settings.aliases\n  const detect = settings.detect || false\n  const languages = settings.languages || lowlight__WEBPACK_IMPORTED_MODULE_0__.grammars\n  const plainText = settings.plainText\n  const prefix = settings.prefix\n  const subset = settings.subset\n  let name = 'hljs'\n\n  const lowlight = (0,lowlight__WEBPACK_IMPORTED_MODULE_1__.createLowlight)(languages)\n\n  if (aliases) {\n    lowlight.registerAlias(aliases)\n  }\n\n  if (prefix) {\n    const pos = prefix.indexOf('-')\n    name = pos === -1 ? prefix : prefix.slice(0, pos)\n  }\n\n  /**\n   * Transform.\n   *\n   * @param {Root} tree\n   *   Tree.\n   * @param {VFile} file\n   *   File.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  return function (tree, file) {\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_2__.visit)(tree, 'element', function (node, _, parent) {\n      if (\n        node.tagName !== 'code' ||\n        !parent ||\n        parent.type !== 'element' ||\n        parent.tagName !== 'pre'\n      ) {\n        return\n      }\n\n      const lang = language(node)\n\n      if (\n        lang === false ||\n        (!lang && !detect) ||\n        (lang && plainText && plainText.includes(lang))\n      ) {\n        return\n      }\n\n      if (!Array.isArray(node.properties.className)) {\n        node.properties.className = []\n      }\n\n      if (!node.properties.className.includes(name)) {\n        node.properties.className.unshift(name)\n      }\n\n      const text = (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__.toText)(node, {whitespace: 'pre'})\n      /** @type {Root} */\n      let result\n\n      try {\n        result = lang\n          ? lowlight.highlight(lang, text, {prefix})\n          : lowlight.highlightAuto(text, {prefix, subset})\n      } catch (error) {\n        const cause = /** @type {Error} */ (error)\n\n        if (lang && /Unknown language/.test(cause.message)) {\n          file.message(\n            'Cannot highlight as `' + lang + '`, it’s not registered',\n            {\n              ancestors: [parent, node],\n              cause,\n              place: node.position,\n              ruleId: 'missing-language',\n              source: 'rehype-highlight'\n            }\n          )\n\n          /* c8 ignore next 5 -- throw arbitrary hljs errors */\n          return\n        }\n\n        throw cause\n      }\n\n      if (!lang && result.data && result.data.language) {\n        node.properties.className.push('language-' + result.data.language)\n      }\n\n      if (result.children.length > 0) {\n        node.children = /** @type {Array<ElementContent>} */ (result.children)\n      }\n    })\n  }\n}\n\n/**\n * Get the programming language of `node`.\n *\n * @param {Element} node\n *   Node.\n * @returns {false | string | undefined}\n *   Language or `undefined`, or `false` when an explikcit `no-highlight` class\n *   is used.\n */\nfunction language(node) {\n  const list = node.properties.className\n  let index = -1\n\n  if (!Array.isArray(list)) {\n    return\n  }\n\n  /** @type {string | undefined} */\n  let name\n\n  while (++index < list.length) {\n    const value = String(list[index])\n\n    if (value === 'no-highlight' || value === 'nohighlight') {\n      return false\n    }\n\n    if (!name && value.slice(0, 5) === 'lang-') {\n      name = value.slice(5)\n    }\n\n    if (!name && value.slice(0, 9) === 'language-') {\n      name = value.slice(9)\n    }\n  }\n\n  return name\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/rehype-highlight/lib/index.js\n");

/***/ })

};
;