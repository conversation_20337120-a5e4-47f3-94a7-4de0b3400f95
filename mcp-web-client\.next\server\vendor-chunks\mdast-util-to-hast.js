"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-to-hast";
exports.ids = ["vendor-chunks/mdast-util-to-hast"];
exports.modules = {

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/footer.js":
/*!********************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/footer.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultFootnoteBackContent: () => (/* binding */ defaultFootnoteBackContent),\n/* harmony export */   defaultFootnoteBackLabel: () => (/* binding */ defaultFootnoteBackLabel),\n/* harmony export */   footer: () => (/* binding */ footer)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/../node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/../node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('./state.js').State} State\n */\n\n/**\n * @callback FootnoteBackContentTemplate\n *   Generate content for the backreference dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent> | ElementContent | string}\n *   Content for the backreference when linking back from definitions to their\n *   reference.\n *\n * @callback FootnoteBackLabelTemplate\n *   Generate a back label dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Back label to use when linking back from definitions to their reference.\n */\n\n\n\n\n/**\n * Generate the default content that GitHub uses on backreferences.\n *\n * @param {number} _\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent>}\n *   Content.\n */\nfunction defaultFootnoteBackContent(_, rereferenceIndex) {\n  /** @type {Array<ElementContent>} */\n  const result = [{type: 'text', value: '↩'}]\n\n  if (rereferenceIndex > 1) {\n    result.push({\n      type: 'element',\n      tagName: 'sup',\n      properties: {},\n      children: [{type: 'text', value: String(rereferenceIndex)}]\n    })\n  }\n\n  return result\n}\n\n/**\n * Generate the default label that GitHub uses on backreferences.\n *\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Label.\n */\nfunction defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n  return (\n    'Back to reference ' +\n    (referenceIndex + 1) +\n    (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n  )\n}\n\n/**\n * Generate a hast footer for called footnote definitions.\n *\n * @param {State} state\n *   Info passed around.\n * @returns {Element | undefined}\n *   `section` element or `undefined`.\n */\n// eslint-disable-next-line complexity\nfunction footer(state) {\n  const clobberPrefix =\n    typeof state.options.clobberPrefix === 'string'\n      ? state.options.clobberPrefix\n      : 'user-content-'\n  const footnoteBackContent =\n    state.options.footnoteBackContent || defaultFootnoteBackContent\n  const footnoteBackLabel =\n    state.options.footnoteBackLabel || defaultFootnoteBackLabel\n  const footnoteLabel = state.options.footnoteLabel || 'Footnotes'\n  const footnoteLabelTagName = state.options.footnoteLabelTagName || 'h2'\n  const footnoteLabelProperties = state.options.footnoteLabelProperties || {\n    className: ['sr-only']\n  }\n  /** @type {Array<ElementContent>} */\n  const listItems = []\n  let referenceIndex = -1\n\n  while (++referenceIndex < state.footnoteOrder.length) {\n    const definition = state.footnoteById.get(\n      state.footnoteOrder[referenceIndex]\n    )\n\n    if (!definition) {\n      continue\n    }\n\n    const content = state.all(definition)\n    const id = String(definition.identifier).toUpperCase()\n    const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(id.toLowerCase())\n    let rereferenceIndex = 0\n    /** @type {Array<ElementContent>} */\n    const backReferences = []\n    const counts = state.footnoteCounts.get(id)\n\n    // eslint-disable-next-line no-unmodified-loop-condition\n    while (counts !== undefined && ++rereferenceIndex <= counts) {\n      if (backReferences.length > 0) {\n        backReferences.push({type: 'text', value: ' '})\n      }\n\n      let children =\n        typeof footnoteBackContent === 'string'\n          ? footnoteBackContent\n          : footnoteBackContent(referenceIndex, rereferenceIndex)\n\n      if (typeof children === 'string') {\n        children = {type: 'text', value: children}\n      }\n\n      backReferences.push({\n        type: 'element',\n        tagName: 'a',\n        properties: {\n          href:\n            '#' +\n            clobberPrefix +\n            'fnref-' +\n            safeId +\n            (rereferenceIndex > 1 ? '-' + rereferenceIndex : ''),\n          dataFootnoteBackref: '',\n          ariaLabel:\n            typeof footnoteBackLabel === 'string'\n              ? footnoteBackLabel\n              : footnoteBackLabel(referenceIndex, rereferenceIndex),\n          className: ['data-footnote-backref']\n        },\n        children: Array.isArray(children) ? children : [children]\n      })\n    }\n\n    const tail = content[content.length - 1]\n\n    if (tail && tail.type === 'element' && tail.tagName === 'p') {\n      const tailTail = tail.children[tail.children.length - 1]\n      if (tailTail && tailTail.type === 'text') {\n        tailTail.value += ' '\n      } else {\n        tail.children.push({type: 'text', value: ' '})\n      }\n\n      tail.children.push(...backReferences)\n    } else {\n      content.push(...backReferences)\n    }\n\n    /** @type {Element} */\n    const listItem = {\n      type: 'element',\n      tagName: 'li',\n      properties: {id: clobberPrefix + 'fn-' + safeId},\n      children: state.wrap(content, true)\n    }\n\n    state.patch(definition, listItem)\n\n    listItems.push(listItem)\n  }\n\n  if (listItems.length === 0) {\n    return\n  }\n\n  return {\n    type: 'element',\n    tagName: 'section',\n    properties: {dataFootnotes: true, className: ['footnotes']},\n    children: [\n      {\n        type: 'element',\n        tagName: footnoteLabelTagName,\n        properties: {\n          ...(0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(footnoteLabelProperties),\n          id: 'footnote-label'\n        },\n        children: [{type: 'text', value: footnoteLabel}]\n      },\n      {type: 'text', value: '\\n'},\n      {\n        type: 'element',\n        tagName: 'ol',\n        properties: {},\n        children: state.wrap(listItems, true)\n      },\n      {type: 'text', value: '\\n'}\n    ]\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/footer.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/blockquote.js":
/*!*********************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/blockquote.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockquote: () => (/* binding */ blockquote)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `blockquote` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Blockquote} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction blockquote(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'blockquote',\n    properties: {},\n    children: state.wrap(state.all(node), true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvYmxvY2txdW90ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQyxhQUFhLDRCQUE0QjtBQUN6QyxhQUFhLDZCQUE2QjtBQUMxQzs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsWUFBWTtBQUN2QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUCxhQUFhLFNBQVM7QUFDdEI7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxzZXJ2ZXJOOE5cXE1DUENsaWVudFxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLWhhc3RcXGxpYlxcaGFuZGxlcnNcXGJsb2NrcXVvdGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5CbG9ja3F1b3RlfSBCbG9ja3F1b3RlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8vIE1ha2UgVlMgQ29kZSBzaG93IHJlZmVyZW5jZXMgdG8gdGhlIGFib3ZlIHR5cGVzLlxuJydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGBibG9ja3F1b3RlYCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge0Jsb2NrcXVvdGV9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBibG9ja3F1b3RlKHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICB0YWdOYW1lOiAnYmxvY2txdW90ZScsXG4gICAgcHJvcGVydGllczoge30sXG4gICAgY2hpbGRyZW46IHN0YXRlLndyYXAoc3RhdGUuYWxsKG5vZGUpLCB0cnVlKVxuICB9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/blockquote.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/break.js":
/*!****************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/break.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hardBreak: () => (/* binding */ hardBreak)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').Break} Break\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `break` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Break} node\n *   mdast node.\n * @returns {Array<Element | Text>}\n *   hast element content.\n */\nfunction hardBreak(state, node) {\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'br', properties: {}, children: []}\n  state.patch(node, result)\n  return [state.applyData(node, result), {type: 'text', value: '\\n'}]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvYnJlYWsuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckMsYUFBYSxxQkFBcUI7QUFDbEMsYUFBYSx1QkFBdUI7QUFDcEMsYUFBYSw2QkFBNkI7QUFDMUM7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1AsYUFBYSxTQUFTO0FBQ3RCLGtCQUFrQiw4Q0FBOEM7QUFDaEU7QUFDQSwwQ0FBMEMsMEJBQTBCO0FBQ3BFIiwic291cmNlcyI6WyJEOlxcc2VydmVyTjhOXFxNQ1BDbGllbnRcXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1oYXN0XFxsaWJcXGhhbmRsZXJzXFxicmVhay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuVGV4dH0gVGV4dFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5CcmVha30gQnJlYWtcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYGJyZWFrYCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge0JyZWFrfSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7QXJyYXk8RWxlbWVudCB8IFRleHQ+fVxuICogICBoYXN0IGVsZW1lbnQgY29udGVudC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGhhcmRCcmVhayhzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHt0eXBlOiAnZWxlbWVudCcsIHRhZ05hbWU6ICdicicsIHByb3BlcnRpZXM6IHt9LCBjaGlsZHJlbjogW119XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIFtzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KSwge3R5cGU6ICd0ZXh0JywgdmFsdWU6ICdcXG4nfV1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/break.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/code.js":
/*!***************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/code.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `code` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Code} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction code(state, node) {\n  const value = node.value ? node.value + '\\n' : ''\n  /** @type {Properties} */\n  const properties = {}\n\n  if (node.lang) {\n    properties.className = ['language-' + node.lang]\n  }\n\n  // Create `<code>`.\n  /** @type {Element} */\n  let result = {\n    type: 'element',\n    tagName: 'code',\n    properties,\n    children: [{type: 'text', value}]\n  }\n\n  if (node.meta) {\n    result.data = {meta: node.meta}\n  }\n\n  state.patch(node, result)\n  result = state.applyData(node, result)\n\n  // Create `<pre>`.\n  result = {type: 'element', tagName: 'pre', properties: {}, children: [result]}\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/code.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/delete.js":
/*!*****************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/delete.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strikethrough: () => (/* binding */ strikethrough)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Delete} Delete\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `delete` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Delete} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction strikethrough(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'del',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvZGVsZXRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDLGFBQWEsd0JBQXdCO0FBQ3JDLGFBQWEsNkJBQTZCO0FBQzFDOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLGFBQWEsU0FBUztBQUN0QjtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHNlcnZlck44TlxcTUNQQ2xpZW50XFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8taGFzdFxcbGliXFxoYW5kbGVyc1xcZGVsZXRlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuRGVsZXRlfSBEZWxldGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYGRlbGV0ZWAgbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtEZWxldGV9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzdHJpa2V0aHJvdWdoKHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICB0YWdOYW1lOiAnZGVsJyxcbiAgICBwcm9wZXJ0aWVzOiB7fSxcbiAgICBjaGlsZHJlbjogc3RhdGUuYWxsKG5vZGUpXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/delete.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/emphasis.js":
/*!*******************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/emphasis.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emphasis: () => (/* binding */ emphasis)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Emphasis} Emphasis\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `emphasis` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Emphasis} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction emphasis(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'em',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvZW1waGFzaXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckMsYUFBYSwwQkFBMEI7QUFDdkMsYUFBYSw2QkFBNkI7QUFDMUM7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLFVBQVU7QUFDckI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1AsYUFBYSxTQUFTO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcc2VydmVyTjhOXFxNQ1BDbGllbnRcXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1oYXN0XFxsaWJcXGhhbmRsZXJzXFxlbXBoYXNpcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkVtcGhhc2lzfSBFbXBoYXNpc1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vLyBNYWtlIFZTIENvZGUgc2hvdyByZWZlcmVuY2VzIHRvIHRoZSBhYm92ZSB0eXBlcy5cbicnXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgZW1waGFzaXNgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7RW1waGFzaXN9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBlbXBoYXNpcyhzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHtcbiAgICB0eXBlOiAnZWxlbWVudCcsXG4gICAgdGFnTmFtZTogJ2VtJyxcbiAgICBwcm9wZXJ0aWVzOiB7fSxcbiAgICBjaGlsZHJlbjogc3RhdGUuYWxsKG5vZGUpXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/emphasis.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   footnoteReference: () => (/* binding */ footnoteReference)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/../node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').FootnoteReference} FootnoteReference\n * @typedef {import('../state.js').State} State\n */\n\n\n\n/**\n * Turn an mdast `footnoteReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {FootnoteReference} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction footnoteReference(state, node) {\n  const clobberPrefix =\n    typeof state.options.clobberPrefix === 'string'\n      ? state.options.clobberPrefix\n      : 'user-content-'\n  const id = String(node.identifier).toUpperCase()\n  const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(id.toLowerCase())\n  const index = state.footnoteOrder.indexOf(id)\n  /** @type {number} */\n  let counter\n\n  let reuseCounter = state.footnoteCounts.get(id)\n\n  if (reuseCounter === undefined) {\n    reuseCounter = 0\n    state.footnoteOrder.push(id)\n    counter = state.footnoteOrder.length\n  } else {\n    counter = index + 1\n  }\n\n  reuseCounter += 1\n  state.footnoteCounts.set(id, reuseCounter)\n\n  /** @type {Element} */\n  const link = {\n    type: 'element',\n    tagName: 'a',\n    properties: {\n      href: '#' + clobberPrefix + 'fn-' + safeId,\n      id:\n        clobberPrefix +\n        'fnref-' +\n        safeId +\n        (reuseCounter > 1 ? '-' + reuseCounter : ''),\n      dataFootnoteRef: true,\n      ariaDescribedBy: ['footnote-label']\n    },\n    children: [{type: 'text', value: String(counter)}]\n  }\n  state.patch(node, link)\n\n  /** @type {Element} */\n  const sup = {\n    type: 'element',\n    tagName: 'sup',\n    properties: {},\n    children: [link]\n  }\n  state.patch(node, sup)\n  return state.applyData(node, sup)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/heading.js":
/*!******************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/heading.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   heading: () => (/* binding */ heading)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `heading` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Heading} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction heading(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'h' + node.depth,\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvaGVhZGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQyxhQUFhLHlCQUF5QjtBQUN0QyxhQUFhLDZCQUE2QjtBQUMxQzs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUCxhQUFhLFNBQVM7QUFDdEI7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxzZXJ2ZXJOOE5cXE1DUENsaWVudFxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLWhhc3RcXGxpYlxcaGFuZGxlcnNcXGhlYWRpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5IZWFkaW5nfSBIZWFkaW5nXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8vIE1ha2UgVlMgQ29kZSBzaG93IHJlZmVyZW5jZXMgdG8gdGhlIGFib3ZlIHR5cGVzLlxuJydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGBoZWFkaW5nYCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge0hlYWRpbmd9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBoZWFkaW5nKHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICB0YWdOYW1lOiAnaCcgKyBub2RlLmRlcHRoLFxuICAgIHByb3BlcnRpZXM6IHt9LFxuICAgIGNoaWxkcmVuOiBzdGF0ZS5hbGwobm9kZSlcbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/heading.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/html.js":
/*!***************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/html.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: () => (/* binding */ html)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Html} Html\n * @typedef {import('../state.js').State} State\n * @typedef {import('../../index.js').Raw} Raw\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `html` node into hast (`raw` node in dangerous mode, otherwise\n * nothing).\n *\n * @param {State} state\n *   Info passed around.\n * @param {Html} node\n *   mdast node.\n * @returns {Element | Raw | undefined}\n *   hast node.\n */\nfunction html(state, node) {\n  if (state.options.allowDangerousHtml) {\n    /** @type {Raw} */\n    const result = {type: 'raw', value: node.value}\n    state.patch(node, result)\n    return state.applyData(node, result)\n  }\n\n  return undefined\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvaHRtbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQyxhQUFhLHNCQUFzQjtBQUNuQyxhQUFhLDZCQUE2QjtBQUMxQyxhQUFhLDhCQUE4QjtBQUMzQzs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQO0FBQ0EsZUFBZSxLQUFLO0FBQ3BCLG9CQUFvQjtBQUNwQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHNlcnZlck44TlxcTUNQQ2xpZW50XFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8taGFzdFxcbGliXFxoYW5kbGVyc1xcaHRtbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkh0bWx9IEh0bWxcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi8uLi9pbmRleC5qcycpLlJhd30gUmF3XG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYGh0bWxgIG5vZGUgaW50byBoYXN0IChgcmF3YCBub2RlIGluIGRhbmdlcm91cyBtb2RlLCBvdGhlcndpc2VcbiAqIG5vdGhpbmcpLlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7SHRtbH0gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0VsZW1lbnQgfCBSYXcgfCB1bmRlZmluZWR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGh0bWwoc3RhdGUsIG5vZGUpIHtcbiAgaWYgKHN0YXRlLm9wdGlvbnMuYWxsb3dEYW5nZXJvdXNIdG1sKSB7XG4gICAgLyoqIEB0eXBlIHtSYXd9ICovXG4gICAgY29uc3QgcmVzdWx0ID0ge3R5cGU6ICdyYXcnLCB2YWx1ZTogbm9kZS52YWx1ZX1cbiAgICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gICAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG4gIH1cblxuICByZXR1cm4gdW5kZWZpbmVkXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/html.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/image-reference.js":
/*!**************************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/image-reference.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   imageReference: () => (/* binding */ imageReference)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/../node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/* harmony import */ var _revert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../revert.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/revert.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('../state.js').State} State\n */\n\n\n\n\n/**\n * Turn an mdast `imageReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ImageReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */\nfunction imageReference(state, node) {\n  const id = String(node.identifier).toUpperCase()\n  const definition = state.definitionById.get(id)\n\n  if (!definition) {\n    return (0,_revert_js__WEBPACK_IMPORTED_MODULE_0__.revert)(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {src: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__.normalizeUri)(definition.url || ''), alt: node.alt}\n\n  if (definition.title !== null && definition.title !== undefined) {\n    properties.title = definition.title\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'img', properties, children: []}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/image-reference.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/image.js":
/*!****************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/image.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   image: () => (/* binding */ image)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/../node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Image} Image\n * @typedef {import('../state.js').State} State\n */\n\n\n\n/**\n * Turn an mdast `image` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Image} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction image(state, node) {\n  /** @type {Properties} */\n  const properties = {src: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(node.url)}\n\n  if (node.alt !== null && node.alt !== undefined) {\n    properties.alt = node.alt\n  }\n\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'img', properties, children: []}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvaW1hZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDLGFBQWEsMkJBQTJCO0FBQ3hDLGFBQWEsdUJBQXVCO0FBQ3BDLGFBQWEsNkJBQTZCO0FBQzFDOztBQUV3RDs7QUFFeEQ7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLGFBQWEsWUFBWTtBQUN6QixzQkFBc0IsS0FBSyx5RUFBWTs7QUFFdkM7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxhQUFhLFNBQVM7QUFDdEIsa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHNlcnZlck44TlxcTUNQQ2xpZW50XFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8taGFzdFxcbGliXFxoYW5kbGVyc1xcaW1hZ2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLlByb3BlcnRpZXN9IFByb3BlcnRpZXNcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuSW1hZ2V9IEltYWdlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbmltcG9ydCB7bm9ybWFsaXplVXJpfSBmcm9tICdtaWNyb21hcmstdXRpbC1zYW5pdGl6ZS11cmknXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgaW1hZ2VgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7SW1hZ2V9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpbWFnZShzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge1Byb3BlcnRpZXN9ICovXG4gIGNvbnN0IHByb3BlcnRpZXMgPSB7c3JjOiBub3JtYWxpemVVcmkobm9kZS51cmwpfVxuXG4gIGlmIChub2RlLmFsdCAhPT0gbnVsbCAmJiBub2RlLmFsdCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgcHJvcGVydGllcy5hbHQgPSBub2RlLmFsdFxuICB9XG5cbiAgaWYgKG5vZGUudGl0bGUgIT09IG51bGwgJiYgbm9kZS50aXRsZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgcHJvcGVydGllcy50aXRsZSA9IG5vZGUudGl0bGVcbiAgfVxuXG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge3R5cGU6ICdlbGVtZW50JywgdGFnTmFtZTogJ2ltZycsIHByb3BlcnRpZXMsIGNoaWxkcmVuOiBbXX1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/image.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/index.js":
/*!****************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handlers: () => (/* binding */ handlers)\n/* harmony export */ });\n/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blockquote.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/blockquote.js\");\n/* harmony import */ var _break_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./break.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/break.js\");\n/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./code.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/code.js\");\n/* harmony import */ var _delete_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./delete.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/delete.js\");\n/* harmony import */ var _emphasis_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emphasis.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/emphasis.js\");\n/* harmony import */ var _footnote_reference_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./footnote-reference.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js\");\n/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./heading.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/heading.js\");\n/* harmony import */ var _html_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./html.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/html.js\");\n/* harmony import */ var _image_reference_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./image-reference.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/image-reference.js\");\n/* harmony import */ var _image_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./image.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/image.js\");\n/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./inline-code.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/inline-code.js\");\n/* harmony import */ var _link_reference_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./link-reference.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/link-reference.js\");\n/* harmony import */ var _link_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./link.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/link.js\");\n/* harmony import */ var _list_item_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./list-item.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/list-item.js\");\n/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./list.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/list.js\");\n/* harmony import */ var _paragraph_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./paragraph.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/paragraph.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./root.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/root.js\");\n/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./strong.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/strong.js\");\n/* harmony import */ var _table_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./table.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/table.js\");\n/* harmony import */ var _table_row_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./table-row.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/table-row.js\");\n/* harmony import */ var _table_cell_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./table-cell.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/table-cell.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./text.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/text.js\");\n/* harmony import */ var _thematic_break_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./thematic-break.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Default handlers for nodes.\n *\n * @satisfies {import('../state.js').Handlers}\n */\nconst handlers = {\n  blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_0__.blockquote,\n  break: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n  code: _code_js__WEBPACK_IMPORTED_MODULE_2__.code,\n  delete: _delete_js__WEBPACK_IMPORTED_MODULE_3__.strikethrough,\n  emphasis: _emphasis_js__WEBPACK_IMPORTED_MODULE_4__.emphasis,\n  footnoteReference: _footnote_reference_js__WEBPACK_IMPORTED_MODULE_5__.footnoteReference,\n  heading: _heading_js__WEBPACK_IMPORTED_MODULE_6__.heading,\n  html: _html_js__WEBPACK_IMPORTED_MODULE_7__.html,\n  imageReference: _image_reference_js__WEBPACK_IMPORTED_MODULE_8__.imageReference,\n  image: _image_js__WEBPACK_IMPORTED_MODULE_9__.image,\n  inlineCode: _inline_code_js__WEBPACK_IMPORTED_MODULE_10__.inlineCode,\n  linkReference: _link_reference_js__WEBPACK_IMPORTED_MODULE_11__.linkReference,\n  link: _link_js__WEBPACK_IMPORTED_MODULE_12__.link,\n  listItem: _list_item_js__WEBPACK_IMPORTED_MODULE_13__.listItem,\n  list: _list_js__WEBPACK_IMPORTED_MODULE_14__.list,\n  paragraph: _paragraph_js__WEBPACK_IMPORTED_MODULE_15__.paragraph,\n  // @ts-expect-error: root is different, but hard to type.\n  root: _root_js__WEBPACK_IMPORTED_MODULE_16__.root,\n  strong: _strong_js__WEBPACK_IMPORTED_MODULE_17__.strong,\n  table: _table_js__WEBPACK_IMPORTED_MODULE_18__.table,\n  tableCell: _table_cell_js__WEBPACK_IMPORTED_MODULE_19__.tableCell,\n  tableRow: _table_row_js__WEBPACK_IMPORTED_MODULE_20__.tableRow,\n  text: _text_js__WEBPACK_IMPORTED_MODULE_21__.text,\n  thematicBreak: _thematic_break_js__WEBPACK_IMPORTED_MODULE_22__.thematicBreak,\n  toml: ignore,\n  yaml: ignore,\n  definition: ignore,\n  footnoteDefinition: ignore\n}\n\n// Return nothing for nodes that are ignored.\nfunction ignore() {\n  return undefined\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/inline-code.js":
/*!**********************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/inline-code.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: () => (/* binding */ inlineCode)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `inlineCode` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {InlineCode} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction inlineCode(state, node) {\n  /** @type {Text} */\n  const text = {type: 'text', value: node.value.replace(/\\r?\\n|\\r/g, ' ')}\n  state.patch(node, text)\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'code',\n    properties: {},\n    children: [text]\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvaW5saW5lLWNvZGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckMsYUFBYSxxQkFBcUI7QUFDbEMsYUFBYSw0QkFBNEI7QUFDekMsYUFBYSw2QkFBNkI7QUFDMUM7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLFlBQVk7QUFDdkI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1AsYUFBYSxNQUFNO0FBQ25CLGdCQUFnQjtBQUNoQjs7QUFFQSxhQUFhLFNBQVM7QUFDdEI7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxzZXJ2ZXJOOE5cXE1DUENsaWVudFxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLWhhc3RcXGxpYlxcaGFuZGxlcnNcXGlubGluZS1jb2RlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5UZXh0fSBUZXh0XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLklubGluZUNvZGV9IElubGluZUNvZGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYGlubGluZUNvZGVgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7SW5saW5lQ29kZX0gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0VsZW1lbnR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlubGluZUNvZGUoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtUZXh0fSAqL1xuICBjb25zdCB0ZXh0ID0ge3R5cGU6ICd0ZXh0JywgdmFsdWU6IG5vZGUudmFsdWUucmVwbGFjZSgvXFxyP1xcbnxcXHIvZywgJyAnKX1cbiAgc3RhdGUucGF0Y2gobm9kZSwgdGV4dClcblxuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHtcbiAgICB0eXBlOiAnZWxlbWVudCcsXG4gICAgdGFnTmFtZTogJ2NvZGUnLFxuICAgIHByb3BlcnRpZXM6IHt9LFxuICAgIGNoaWxkcmVuOiBbdGV4dF1cbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/inline-code.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/link-reference.js":
/*!*************************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/link-reference.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   linkReference: () => (/* binding */ linkReference)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/../node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/* harmony import */ var _revert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../revert.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/revert.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('../state.js').State} State\n */\n\n\n\n\n/**\n * Turn an mdast `linkReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {LinkReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */\nfunction linkReference(state, node) {\n  const id = String(node.identifier).toUpperCase()\n  const definition = state.definitionById.get(id)\n\n  if (!definition) {\n    return (0,_revert_js__WEBPACK_IMPORTED_MODULE_0__.revert)(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {href: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__.normalizeUri)(definition.url || '')}\n\n  if (definition.title !== null && definition.title !== undefined) {\n    properties.title = definition.title\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/link-reference.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/link.js":
/*!***************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/link.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   link: () => (/* binding */ link)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/../node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Link} Link\n * @typedef {import('../state.js').State} State\n */\n\n\n\n/**\n * Turn an mdast `link` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Link} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction link(state, node) {\n  /** @type {Properties} */\n  const properties = {href: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(node.url)}\n\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvbGluay5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckMsYUFBYSwyQkFBMkI7QUFDeEMsYUFBYSxzQkFBc0I7QUFDbkMsYUFBYSw2QkFBNkI7QUFDMUM7O0FBRXdEOztBQUV4RDtBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLE1BQU07QUFDakI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1AsYUFBYSxZQUFZO0FBQ3pCLHNCQUFzQixNQUFNLHlFQUFZOztBQUV4QztBQUNBO0FBQ0E7O0FBRUEsYUFBYSxTQUFTO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHNlcnZlck44TlxcTUNQQ2xpZW50XFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8taGFzdFxcbGliXFxoYW5kbGVyc1xcbGluay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuUHJvcGVydGllc30gUHJvcGVydGllc1xuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5MaW5rfSBMaW5rXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbmltcG9ydCB7bm9ybWFsaXplVXJpfSBmcm9tICdtaWNyb21hcmstdXRpbC1zYW5pdGl6ZS11cmknXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgbGlua2Agbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtMaW5rfSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7RWxlbWVudH1cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gbGluayhzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge1Byb3BlcnRpZXN9ICovXG4gIGNvbnN0IHByb3BlcnRpZXMgPSB7aHJlZjogbm9ybWFsaXplVXJpKG5vZGUudXJsKX1cblxuICBpZiAobm9kZS50aXRsZSAhPT0gbnVsbCAmJiBub2RlLnRpdGxlICE9PSB1bmRlZmluZWQpIHtcbiAgICBwcm9wZXJ0aWVzLnRpdGxlID0gbm9kZS50aXRsZVxuICB9XG5cbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICdhJyxcbiAgICBwcm9wZXJ0aWVzLFxuICAgIGNoaWxkcmVuOiBzdGF0ZS5hbGwobm9kZSlcbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/link.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/list-item.js":
/*!********************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/list-item.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItem: () => (/* binding */ listItem)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Parents} Parents\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `listItem` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ListItem} node\n *   mdast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nfunction listItem(state, node, parent) {\n  const results = state.all(node)\n  const loose = parent ? listLoose(parent) : listItemLoose(node)\n  /** @type {Properties} */\n  const properties = {}\n  /** @type {Array<ElementContent>} */\n  const children = []\n\n  if (typeof node.checked === 'boolean') {\n    const head = results[0]\n    /** @type {Element} */\n    let paragraph\n\n    if (head && head.type === 'element' && head.tagName === 'p') {\n      paragraph = head\n    } else {\n      paragraph = {type: 'element', tagName: 'p', properties: {}, children: []}\n      results.unshift(paragraph)\n    }\n\n    if (paragraph.children.length > 0) {\n      paragraph.children.unshift({type: 'text', value: ' '})\n    }\n\n    paragraph.children.unshift({\n      type: 'element',\n      tagName: 'input',\n      properties: {type: 'checkbox', checked: node.checked, disabled: true},\n      children: []\n    })\n\n    // According to github-markdown-css, this class hides bullet.\n    // See: <https://github.com/sindresorhus/github-markdown-css>.\n    properties.className = ['task-list-item']\n  }\n\n  let index = -1\n\n  while (++index < results.length) {\n    const child = results[index]\n\n    // Add eols before nodes, except if this is a loose, first paragraph.\n    if (\n      loose ||\n      index !== 0 ||\n      child.type !== 'element' ||\n      child.tagName !== 'p'\n    ) {\n      children.push({type: 'text', value: '\\n'})\n    }\n\n    if (child.type === 'element' && child.tagName === 'p' && !loose) {\n      children.push(...child.children)\n    } else {\n      children.push(child)\n    }\n  }\n\n  const tail = results[results.length - 1]\n\n  // Add a final eol.\n  if (tail && (loose || tail.type !== 'element' || tail.tagName !== 'p')) {\n    children.push({type: 'text', value: '\\n'})\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'li', properties, children}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n\n/**\n * @param {Parents} node\n * @return {Boolean}\n */\nfunction listLoose(node) {\n  let loose = false\n  if (node.type === 'list') {\n    loose = node.spread || false\n    const children = node.children\n    let index = -1\n\n    while (!loose && ++index < children.length) {\n      loose = listItemLoose(children[index])\n    }\n  }\n\n  return loose\n}\n\n/**\n * @param {ListItem} node\n * @return {Boolean}\n */\nfunction listItemLoose(node) {\n  const spread = node.spread\n\n  return spread === null || spread === undefined\n    ? node.children.length > 1\n    : spread\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/list-item.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/list.js":
/*!***************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/list.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').List} List\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `list` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {List} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction list(state, node) {\n  /** @type {Properties} */\n  const properties = {}\n  const results = state.all(node)\n  let index = -1\n\n  if (typeof node.start === 'number' && node.start !== 1) {\n    properties.start = node.start\n  }\n\n  // Like GitHub, add a class for custom styling.\n  while (++index < results.length) {\n    const child = results[index]\n\n    if (\n      child.type === 'element' &&\n      child.tagName === 'li' &&\n      child.properties &&\n      Array.isArray(child.properties.className) &&\n      child.properties.className.includes('task-list-item')\n    ) {\n      properties.className = ['contains-task-list']\n      break\n    }\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: node.ordered ? 'ol' : 'ul',\n    properties,\n    children: state.wrap(results, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/list.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/paragraph.js":
/*!********************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/paragraph.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   paragraph: () => (/* binding */ paragraph)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `paragraph` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Paragraph} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction paragraph(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'p',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvcGFyYWdyYXBoLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDLGFBQWEsMkJBQTJCO0FBQ3hDLGFBQWEsNkJBQTZCO0FBQzFDOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsV0FBVyxXQUFXO0FBQ3RCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLGFBQWEsU0FBUztBQUN0QjtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHNlcnZlck44TlxcTUNQQ2xpZW50XFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8taGFzdFxcbGliXFxoYW5kbGVyc1xccGFyYWdyYXBoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuUGFyYWdyYXBofSBQYXJhZ3JhcGhcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYHBhcmFncmFwaGAgbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtQYXJhZ3JhcGh9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwYXJhZ3JhcGgoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICdwJyxcbiAgICBwcm9wZXJ0aWVzOiB7fSxcbiAgICBjaGlsZHJlbjogc3RhdGUuYWxsKG5vZGUpXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/paragraph.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/root.js":
/*!***************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/root.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Parents} HastParents\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `root` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastRoot} node\n *   mdast node.\n * @returns {HastParents}\n *   hast node.\n */\nfunction root(state, node) {\n  /** @type {HastRoot} */\n  const result = {type: 'root', children: state.wrap(state.all(node))}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvcm9vdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQyxhQUFhLHFCQUFxQjtBQUNsQyxhQUFhLHNCQUFzQjtBQUNuQyxhQUFhLDZCQUE2QjtBQUMxQzs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsV0FBVztBQUN0QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUCxhQUFhLFVBQVU7QUFDdkIsa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHNlcnZlck44TlxcTUNQQ2xpZW50XFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8taGFzdFxcbGliXFxoYW5kbGVyc1xccm9vdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5QYXJlbnRzfSBIYXN0UGFyZW50c1xuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLlJvb3R9IEhhc3RSb290XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlJvb3R9IE1kYXN0Um9vdFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vLyBNYWtlIFZTIENvZGUgc2hvdyByZWZlcmVuY2VzIHRvIHRoZSBhYm92ZSB0eXBlcy5cbicnXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgcm9vdGAgbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtNZGFzdFJvb3R9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtIYXN0UGFyZW50c31cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gcm9vdChzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0hhc3RSb290fSAqL1xuICBjb25zdCByZXN1bHQgPSB7dHlwZTogJ3Jvb3QnLCBjaGlsZHJlbjogc3RhdGUud3JhcChzdGF0ZS5hbGwobm9kZSkpfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/root.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/strong.js":
/*!*****************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/strong.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strong: () => (/* binding */ strong)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Strong} Strong\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `strong` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Strong} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction strong(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'strong',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvc3Ryb25nLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDLGFBQWEsd0JBQXdCO0FBQ3JDLGFBQWEsNkJBQTZCO0FBQzFDOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLGFBQWEsU0FBUztBQUN0QjtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHNlcnZlck44TlxcTUNQQ2xpZW50XFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8taGFzdFxcbGliXFxoYW5kbGVyc1xcc3Ryb25nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuU3Ryb25nfSBTdHJvbmdcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYHN0cm9uZ2Agbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtTdHJvbmd9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzdHJvbmcoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICdzdHJvbmcnLFxuICAgIHByb3BlcnRpZXM6IHt9LFxuICAgIGNoaWxkcmVuOiBzdGF0ZS5hbGwobm9kZSlcbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/strong.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/table-cell.js":
/*!*********************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/table-cell.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableCell: () => (/* binding */ tableCell)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').TableCell} TableCell\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `tableCell` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableCell} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction tableCell(state, node) {\n  // Note: this function is normally not called: see `table-row` for how rows\n  // and their cells are compiled.\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'td', // Assume body cell.\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvdGFibGUtY2VsbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQyxhQUFhLDJCQUEyQjtBQUN4QyxhQUFhLDZCQUE2QjtBQUMxQzs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsV0FBVztBQUN0QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0EsYUFBYSxTQUFTO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcc2VydmVyTjhOXFxNQ1BDbGllbnRcXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1oYXN0XFxsaWJcXGhhbmRsZXJzXFx0YWJsZS1jZWxsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuVGFibGVDZWxsfSBUYWJsZUNlbGxcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYHRhYmxlQ2VsbGAgbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtUYWJsZUNlbGx9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0YWJsZUNlbGwoc3RhdGUsIG5vZGUpIHtcbiAgLy8gTm90ZTogdGhpcyBmdW5jdGlvbiBpcyBub3JtYWxseSBub3QgY2FsbGVkOiBzZWUgYHRhYmxlLXJvd2AgZm9yIGhvdyByb3dzXG4gIC8vIGFuZCB0aGVpciBjZWxscyBhcmUgY29tcGlsZWQuXG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICB0YWdOYW1lOiAndGQnLCAvLyBBc3N1bWUgYm9keSBjZWxsLlxuICAgIHByb3BlcnRpZXM6IHt9LFxuICAgIGNoaWxkcmVuOiBzdGF0ZS5hbGwobm9kZSlcbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/table-cell.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/table-row.js":
/*!********************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/table-row.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableRow: () => (/* binding */ tableRow)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Parents} Parents\n * @typedef {import('mdast').TableRow} TableRow\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `tableRow` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableRow} node\n *   mdast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nfunction tableRow(state, node, parent) {\n  const siblings = parent ? parent.children : undefined\n  // Generate a body row when without parent.\n  const rowIndex = siblings ? siblings.indexOf(node) : 1\n  const tagName = rowIndex === 0 ? 'th' : 'td'\n  // To do: option to use `style`?\n  const align = parent && parent.type === 'table' ? parent.align : undefined\n  const length = align ? align.length : node.children.length\n  let cellIndex = -1\n  /** @type {Array<ElementContent>} */\n  const cells = []\n\n  while (++cellIndex < length) {\n    // Note: can also be undefined.\n    const cell = node.children[cellIndex]\n    /** @type {Properties} */\n    const properties = {}\n    const alignValue = align ? align[cellIndex] : undefined\n\n    if (alignValue) {\n      properties.align = alignValue\n    }\n\n    /** @type {Element} */\n    let result = {type: 'element', tagName, properties, children: []}\n\n    if (cell) {\n      result.children = state.all(cell)\n      state.patch(cell, result)\n      result = state.applyData(cell, result)\n    }\n\n    cells.push(result)\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'tr',\n    properties: {},\n    children: state.wrap(cells, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/table-row.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/table.js":
/*!****************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/table.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   table: () => (/* binding */ table)\n/* harmony export */ });\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/../node_modules/unist-util-position/lib/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Table} Table\n * @typedef {import('../state.js').State} State\n */\n\n\n\n/**\n * Turn an mdast `table` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Table} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction table(state, node) {\n  const rows = state.all(node)\n  const firstRow = rows.shift()\n  /** @type {Array<Element>} */\n  const tableContent = []\n\n  if (firstRow) {\n    /** @type {Element} */\n    const head = {\n      type: 'element',\n      tagName: 'thead',\n      properties: {},\n      children: state.wrap([firstRow], true)\n    }\n    state.patch(node.children[0], head)\n    tableContent.push(head)\n  }\n\n  if (rows.length > 0) {\n    /** @type {Element} */\n    const body = {\n      type: 'element',\n      tagName: 'tbody',\n      properties: {},\n      children: state.wrap(rows, true)\n    }\n\n    const start = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_0__.pointStart)(node.children[1])\n    const end = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_0__.pointEnd)(node.children[node.children.length - 1])\n    if (start && end) body.position = {start, end}\n    tableContent.push(body)\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'table',\n    properties: {},\n    children: state.wrap(tableContent, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/table.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/text.js":
/*!***************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/text.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var trim_lines__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! trim-lines */ \"(ssr)/../node_modules/trim-lines/index.js\");\n/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').Text} HastText\n * @typedef {import('mdast').Text} MdastText\n * @typedef {import('../state.js').State} State\n */\n\n\n\n/**\n * Turn an mdast `text` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastText} node\n *   mdast node.\n * @returns {HastElement | HastText}\n *   hast node.\n */\nfunction text(state, node) {\n  /** @type {HastText} */\n  const result = {type: 'text', value: (0,trim_lines__WEBPACK_IMPORTED_MODULE_0__.trimLines)(String(node.value))}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvdGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckMsYUFBYSxxQkFBcUI7QUFDbEMsYUFBYSxzQkFBc0I7QUFDbkMsYUFBYSw2QkFBNkI7QUFDMUM7O0FBRW9DOztBQUVwQztBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLFdBQVc7QUFDdEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1AsYUFBYSxVQUFVO0FBQ3ZCLGtCQUFrQixxQkFBcUIscURBQVM7QUFDaEQ7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcc2VydmVyTjhOXFxNQ1BDbGllbnRcXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1oYXN0XFxsaWJcXGhhbmRsZXJzXFx0ZXh0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEhhc3RFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuVGV4dH0gSGFzdFRleHRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuVGV4dH0gTWRhc3RUZXh0XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbmltcG9ydCB7dHJpbUxpbmVzfSBmcm9tICd0cmltLWxpbmVzJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYHRleHRgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7TWRhc3RUZXh0fSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7SGFzdEVsZW1lbnQgfCBIYXN0VGV4dH1cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gdGV4dChzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0hhc3RUZXh0fSAqL1xuICBjb25zdCByZXN1bHQgPSB7dHlwZTogJ3RleHQnLCB2YWx1ZTogdHJpbUxpbmVzKFN0cmluZyhub2RlLnZhbHVlKSl9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/text.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js":
/*!*************************************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   thematicBreak: () => (/* binding */ thematicBreak)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `thematicBreak` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ThematicBreak} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction thematicBreak(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'hr',\n    properties: {},\n    children: []\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvdGhlbWF0aWMtYnJlYWsuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckMsYUFBYSwrQkFBK0I7QUFDNUMsYUFBYSw2QkFBNkI7QUFDMUM7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLGVBQWU7QUFDMUI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1AsYUFBYSxTQUFTO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcc2VydmVyTjhOXFxNQ1BDbGllbnRcXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1oYXN0XFxsaWJcXGhhbmRsZXJzXFx0aGVtYXRpYy1icmVhay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlRoZW1hdGljQnJlYWt9IFRoZW1hdGljQnJlYWtcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYHRoZW1hdGljQnJlYWtgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7VGhlbWF0aWNCcmVha30gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0VsZW1lbnR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRoZW1hdGljQnJlYWsoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICdocicsXG4gICAgcHJvcGVydGllczoge30sXG4gICAgY2hpbGRyZW46IFtdXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/index.js":
/*!*******************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toHast: () => (/* binding */ toHast)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/../node_modules/devlop/lib/development.js\");\n/* harmony import */ var _footer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./footer.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/footer.js\");\n/* harmony import */ var _state_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./state.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/state.js\");\n/**\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('./state.js').Options} Options\n */\n\n\n\n\n\n/**\n * Transform mdast to hast.\n *\n * ##### Notes\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most utilities ignore `raw` nodes but two notable ones don’t:\n *\n * *   `hast-util-to-html` also has an option `allowDangerousHtml` which will\n *     output the raw HTML.\n *     This is typically discouraged as noted by the option name but is useful\n *     if you completely trust authors\n * *   `hast-util-raw` can handle the raw embedded HTML strings by parsing them\n *     into standard hast nodes (`element`, `text`, etc).\n *     This is a heavy task as it needs a full HTML parser, but it is the only\n *     way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark, which we follow by default.\n * They are supported by GitHub, so footnotes can be enabled in markdown with\n * `mdast-util-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes, which is hidden for sighted users but shown to\n * assistive technology.\n * When your page is not in English, you must define translated values.\n *\n * Back references use ARIA attributes, but the section label itself uses a\n * heading that is hidden with an `sr-only` class.\n * To show it to sighted users, define different attributes in\n * `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem, as it links footnote calls to footnote\n * definitions on the page through `id` attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * Example: headings (DOM clobbering) in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * *   when the node has a `value` (and doesn’t have `data.hName`,\n *     `data.hProperties`, or `data.hChildren`, see later), create a hast `text`\n *     node\n * *   otherwise, create a `<div>` element (which could be changed with\n *     `data.hName`), with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @param {MdastNodes} tree\n *   mdast tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {HastNodes}\n *   hast tree.\n */\nfunction toHast(tree, options) {\n  const state = (0,_state_js__WEBPACK_IMPORTED_MODULE_0__.createState)(tree, options)\n  const node = state.one(tree, undefined)\n  const foot = (0,_footer_js__WEBPACK_IMPORTED_MODULE_1__.footer)(state)\n  /** @type {HastNodes} */\n  const result = Array.isArray(node)\n    ? {type: 'root', children: node}\n    : node || {type: 'root', children: []}\n\n  if (foot) {\n    // If there’s a footer, there were definitions, meaning block\n    // content.\n    // So `result` is a parent node.\n    (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)('children' in result)\n    result.children.push({type: 'text', value: '\\n'}, foot)\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/revert.js":
/*!********************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/revert.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   revert: () => (/* binding */ revert)\n/* harmony export */ });\n/**\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('mdast').Nodes} Nodes\n * @typedef {import('mdast').Reference} Reference\n *\n * @typedef {import('./state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Return the content of a reference without definition as plain text.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Extract<Nodes, Reference>} node\n *   Reference node (image, link).\n * @returns {Array<ElementContent>}\n *   hast content.\n */\nfunction revert(state, node) {\n  const subtype = node.referenceType\n  let suffix = ']'\n\n  if (subtype === 'collapsed') {\n    suffix += '[]'\n  } else if (subtype === 'full') {\n    suffix += '[' + (node.label || node.identifier) + ']'\n  }\n\n  if (node.type === 'imageReference') {\n    return [{type: 'text', value: '![' + node.alt + suffix}]\n  }\n\n  const contents = state.all(node)\n  const head = contents[0]\n\n  if (head && head.type === 'text') {\n    head.value = '[' + head.value\n  } else {\n    contents.unshift({type: 'text', value: '['})\n  }\n\n  const tail = contents[contents.length - 1]\n\n  if (tail && tail.type === 'text') {\n    tail.value += suffix\n  } else {\n    contents.push({type: 'text', value: suffix})\n  }\n\n  return contents\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/revert.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/mdast-util-to-hast/lib/state.js":
/*!*******************************************************!*\
  !*** ../node_modules/mdast-util-to-hast/lib/state.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createState: () => (/* binding */ createState),\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/../node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/../node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/../node_modules/unist-util-position/lib/index.js\");\n/* harmony import */ var _handlers_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./handlers/index.js */ \"(ssr)/../node_modules/mdast-util-to-hast/lib/handlers/index.js\");\n/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').ElementContent} HastElementContent\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('hast').Properties} HastProperties\n * @typedef {import('hast').RootContent} HastRootContent\n * @typedef {import('hast').Text} HastText\n *\n * @typedef {import('mdast').Definition} MdastDefinition\n * @typedef {import('mdast').FootnoteDefinition} MdastFootnoteDefinition\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('mdast').Parents} MdastParents\n *\n * @typedef {import('vfile').VFile} VFile\n *\n * @typedef {import('./footer.js').FootnoteBackContentTemplate} FootnoteBackContentTemplate\n * @typedef {import('./footer.js').FootnoteBackLabelTemplate} FootnoteBackLabelTemplate\n */\n\n/**\n * @callback Handler\n *   Handle a node.\n * @param {State} state\n *   Info passed around.\n * @param {any} node\n *   mdast node to handle.\n * @param {MdastParents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<HastElementContent> | HastElementContent | undefined}\n *   hast node.\n *\n * @typedef {Partial<Record<MdastNodes['type'], Handler>>} Handlers\n *   Handle nodes.\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Whether to persist raw HTML in markdown in the hast tree (default:\n *   `false`).\n * @property {string | null | undefined} [clobberPrefix='user-content-']\n *   Prefix to use before the `id` property on footnotes to prevent them from\n *   *clobbering* (default: `'user-content-'`).\n *\n *   Pass `''` for trusted markdown and when you are careful with\n *   polyfilling.\n *   You could pass a different prefix.\n *\n *   DOM clobbering is this:\n *\n *   ```html\n *   <p id=\"x\"></p>\n *   <script>alert(x) // `x` now refers to the `p#x` DOM element</script>\n *   ```\n *\n *   The above example shows that elements are made available by browsers, by\n *   their ID, on the `window` object.\n *   This is a security risk because you might be expecting some other variable\n *   at that place.\n *   It can also break polyfills.\n *   Using a prefix solves these problems.\n * @property {VFile | null | undefined} [file]\n *   Corresponding virtual file representing the input document (optional).\n * @property {FootnoteBackContentTemplate | string | null | undefined} [footnoteBackContent]\n *   Content of the backreference back to references (default: `defaultFootnoteBackContent`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackContent(_, rereferenceIndex) {\n *     const result = [{type: 'text', value: '↩'}]\n *\n *     if (rereferenceIndex > 1) {\n *       result.push({\n *         type: 'element',\n *         tagName: 'sup',\n *         properties: {},\n *         children: [{type: 'text', value: String(rereferenceIndex)}]\n *       })\n *     }\n *\n *     return result\n *   }\n *   ```\n *\n *   This content is used in the `a` element of each backreference (the `↩`\n *   links).\n * @property {FootnoteBackLabelTemplate | string | null | undefined} [footnoteBackLabel]\n *   Label to describe the backreference back to references (default:\n *   `defaultFootnoteBackLabel`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n *    return (\n *      'Back to reference ' +\n *      (referenceIndex + 1) +\n *      (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n *    )\n *   }\n *   ```\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is used in the `ariaLabel` property on each backreference\n *   (the `↩` links).\n *   It affects users of assistive technology.\n * @property {string | null | undefined} [footnoteLabel='Footnotes']\n *   Textual label to use for the footnotes section (default: `'Footnotes'`).\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {HastProperties | null | undefined} [footnoteLabelProperties={className: ['sr-only']}]\n *   Properties to use on the footnote label (default: `{className:\n *   ['sr-only']}`).\n *\n *   Change it to show the label and add other properties.\n *\n *   This label is typically hidden visually (assuming an `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass an empty string.\n *   You can also add different properties.\n *\n *   > **Note**: `id: 'footnote-label'` is always added, because footnote\n *   > calls use it with `aria-describedby` to provide an accessible label.\n * @property {string | null | undefined} [footnoteLabelTagName='h2']\n *   HTML tag name to use for the footnote label element (default: `'h2'`).\n *\n *   Change it to match your document structure.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {Handlers | null | undefined} [handlers]\n *   Extra handlers for nodes (optional).\n * @property {Array<MdastNodes['type']> | null | undefined} [passThrough]\n *   List of custom mdast node types to pass through (keep) in hast (note that\n *   the node itself is passed, but eventual children are transformed)\n *   (optional).\n * @property {Handler | null | undefined} [unknownHandler]\n *   Handler for all unknown nodes (optional).\n *\n * @typedef State\n *   Info passed around.\n * @property {(node: MdastNodes) => Array<HastElementContent>} all\n *   Transform the children of an mdast parent to hast.\n * @property {<Type extends HastNodes>(from: MdastNodes, to: Type) => HastElement | Type} applyData\n *   Honor the `data` of `from`, and generate an element instead of `node`.\n * @property {Map<string, MdastDefinition>} definitionById\n *   Definitions by their identifier.\n * @property {Map<string, MdastFootnoteDefinition>} footnoteById\n *   Footnote definitions by their identifier.\n * @property {Map<string, number>} footnoteCounts\n *   Counts for how often the same footnote was called.\n * @property {Array<string>} footnoteOrder\n *   Identifiers of order when footnote calls first appear in tree order.\n * @property {Handlers} handlers\n *   Applied handlers.\n * @property {(node: MdastNodes, parent: MdastParents | undefined) => Array<HastElementContent> | HastElementContent | undefined} one\n *   Transform an mdast node to hast.\n * @property {Options} options\n *   Configuration.\n * @property {(from: MdastNodes, node: HastNodes) => undefined} patch\n *   Copy a node’s positional info.\n * @property {<Type extends HastRootContent>(nodes: Array<Type>, loose?: boolean | undefined) => Array<HastText | Type>} wrap\n *   Wrap `nodes` with line endings between each node, adds initial/final line endings when `loose`.\n */\n\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Create `state` from an mdast tree.\n *\n * @param {MdastNodes} tree\n *   mdast node to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {State}\n *   `state` function.\n */\nfunction createState(tree, options) {\n  const settings = options || emptyOptions\n  /** @type {Map<string, MdastDefinition>} */\n  const definitionById = new Map()\n  /** @type {Map<string, MdastFootnoteDefinition>} */\n  const footnoteById = new Map()\n  /** @type {Map<string, number>} */\n  const footnoteCounts = new Map()\n  /** @type {Handlers} */\n  // @ts-expect-error: the root handler returns a root.\n  // Hard to type.\n  const handlers = {..._handlers_index_js__WEBPACK_IMPORTED_MODULE_0__.handlers, ...settings.handlers}\n\n  /** @type {State} */\n  const state = {\n    all,\n    applyData,\n    definitionById,\n    footnoteById,\n    footnoteCounts,\n    footnoteOrder: [],\n    handlers,\n    one,\n    options: settings,\n    patch,\n    wrap\n  }\n\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.visit)(tree, function (node) {\n    if (node.type === 'definition' || node.type === 'footnoteDefinition') {\n      const map = node.type === 'definition' ? definitionById : footnoteById\n      const id = String(node.identifier).toUpperCase()\n\n      // Mimick CM behavior of link definitions.\n      // See: <https://github.com/syntax-tree/mdast-util-definitions/blob/9032189/lib/index.js#L20-L21>.\n      if (!map.has(id)) {\n        // @ts-expect-error: node type matches map.\n        map.set(id, node)\n      }\n    }\n  })\n\n  return state\n\n  /**\n   * Transform an mdast node into a hast node.\n   *\n   * @param {MdastNodes} node\n   *   mdast node.\n   * @param {MdastParents | undefined} [parent]\n   *   Parent of `node`.\n   * @returns {Array<HastElementContent> | HastElementContent | undefined}\n   *   Resulting hast node.\n   */\n  function one(node, parent) {\n    const type = node.type\n    const handle = state.handlers[type]\n\n    if (own.call(state.handlers, type) && handle) {\n      return handle(state, node, parent)\n    }\n\n    if (state.options.passThrough && state.options.passThrough.includes(type)) {\n      if ('children' in node) {\n        const {children, ...shallow} = node\n        const result = (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(shallow)\n        // @ts-expect-error: TS doesn’t understand…\n        result.children = state.all(node)\n        // @ts-expect-error: TS doesn’t understand…\n        return result\n      }\n\n      // @ts-expect-error: it’s custom.\n      return (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(node)\n    }\n\n    const unknown = state.options.unknownHandler || defaultUnknownHandler\n\n    return unknown(state, node, parent)\n  }\n\n  /**\n   * Transform the children of an mdast node into hast nodes.\n   *\n   * @param {MdastNodes} parent\n   *   mdast node to compile\n   * @returns {Array<HastElementContent>}\n   *   Resulting hast nodes.\n   */\n  function all(parent) {\n    /** @type {Array<HastElementContent>} */\n    const values = []\n\n    if ('children' in parent) {\n      const nodes = parent.children\n      let index = -1\n      while (++index < nodes.length) {\n        const result = state.one(nodes[index], parent)\n\n        // To do: see if we van clean this? Can we merge texts?\n        if (result) {\n          if (index && nodes[index - 1].type === 'break') {\n            if (!Array.isArray(result) && result.type === 'text') {\n              result.value = trimMarkdownSpaceStart(result.value)\n            }\n\n            if (!Array.isArray(result) && result.type === 'element') {\n              const head = result.children[0]\n\n              if (head && head.type === 'text') {\n                head.value = trimMarkdownSpaceStart(head.value)\n              }\n            }\n          }\n\n          if (Array.isArray(result)) {\n            values.push(...result)\n          } else {\n            values.push(result)\n          }\n        }\n      }\n    }\n\n    return values\n  }\n}\n\n/**\n * Copy a node’s positional info.\n *\n * @param {MdastNodes} from\n *   mdast node to copy from.\n * @param {HastNodes} to\n *   hast node to copy into.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(from, to) {\n  if (from.position) to.position = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_3__.position)(from)\n}\n\n/**\n * Honor the `data` of `from` and maybe generate an element instead of `to`.\n *\n * @template {HastNodes} Type\n *   Node type.\n * @param {MdastNodes} from\n *   mdast node to use data from.\n * @param {Type} to\n *   hast node to change.\n * @returns {HastElement | Type}\n *   Nothing.\n */\nfunction applyData(from, to) {\n  /** @type {HastElement | Type} */\n  let result = to\n\n  // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n  if (from && from.data) {\n    const hName = from.data.hName\n    const hChildren = from.data.hChildren\n    const hProperties = from.data.hProperties\n\n    if (typeof hName === 'string') {\n      // Transforming the node resulted in an element with a different name\n      // than wanted:\n      if (result.type === 'element') {\n        result.tagName = hName\n      }\n      // Transforming the node resulted in a non-element, which happens for\n      // raw, text, and root nodes (unless custom handlers are passed).\n      // The intent of `hName` is to create an element, but likely also to keep\n      // the content around (otherwise: pass `hChildren`).\n      else {\n        /** @type {Array<HastElementContent>} */\n        // @ts-expect-error: assume no doctypes in `root`.\n        const children = 'children' in result ? result.children : [result]\n        result = {type: 'element', tagName: hName, properties: {}, children}\n      }\n    }\n\n    if (result.type === 'element' && hProperties) {\n      Object.assign(result.properties, (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(hProperties))\n    }\n\n    if (\n      'children' in result &&\n      result.children &&\n      hChildren !== null &&\n      hChildren !== undefined\n    ) {\n      result.children = hChildren\n    }\n  }\n\n  return result\n}\n\n/**\n * Transform an unknown node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   Unknown mdast node.\n * @returns {HastElement | HastText}\n *   Resulting hast node.\n */\nfunction defaultUnknownHandler(state, node) {\n  const data = node.data || {}\n  /** @type {HastElement | HastText} */\n  const result =\n    'value' in node &&\n    !(own.call(data, 'hProperties') || own.call(data, 'hChildren'))\n      ? {type: 'text', value: node.value}\n      : {\n          type: 'element',\n          tagName: 'div',\n          properties: {},\n          children: state.all(node)\n        }\n\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n\n/**\n * Wrap `nodes` with line endings between each node.\n *\n * @template {HastRootContent} Type\n *   Node type.\n * @param {Array<Type>} nodes\n *   List of nodes to wrap.\n * @param {boolean | undefined} [loose=false]\n *   Whether to add line endings at start and end (default: `false`).\n * @returns {Array<HastText | Type>}\n *   Wrapped nodes.\n */\nfunction wrap(nodes, loose) {\n  /** @type {Array<HastText | Type>} */\n  const result = []\n  let index = -1\n\n  if (loose) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  while (++index < nodes.length) {\n    if (index) result.push({type: 'text', value: '\\n'})\n    result.push(nodes[index])\n  }\n\n  if (loose && nodes.length > 0) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  return result\n}\n\n/**\n * Trim spaces and tabs at the start of `value`.\n *\n * @param {string} value\n *   Value to trim.\n * @returns {string}\n *   Result.\n */\nfunction trimMarkdownSpaceStart(value) {\n  let index = 0\n  let code = value.charCodeAt(index)\n\n  while (code === 9 || code === 32) {\n    index++\n    code = value.charCodeAt(index)\n  }\n\n  return value.slice(index)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/mdast-util-to-hast/lib/state.js\n");

/***/ })

};
;