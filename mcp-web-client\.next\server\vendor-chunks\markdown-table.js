"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/markdown-table";
exports.ids = ["vendor-chunks/markdown-table"];
exports.modules = {

/***/ "(ssr)/../node_modules/markdown-table/index.js":
/*!***********************************************!*\
  !*** ../node_modules/markdown-table/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   markdownTable: () => (/* binding */ markdownTable)\n/* harmony export */ });\n// To do: next major: remove.\n/**\n * @typedef {Options} MarkdownTableOptions\n *   Configuration.\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [alignDelimiters=true]\n *   Whether to align the delimiters (default: `true`);\n *   they are aligned by default:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   Pass `false` to make them staggered:\n *\n *   ```markdown\n *   | Alpha | B |\n *   | - | - |\n *   | C | Delta |\n *   ```\n * @property {ReadonlyArray<string | null | undefined> | string | null | undefined} [align]\n *   How to align columns (default: `''`);\n *   one style for all columns or styles for their respective columns;\n *   each style is either `'l'` (left), `'r'` (right), or `'c'` (center);\n *   other values are treated as `''`, which doesn’t place the colon in the\n *   alignment row but does align left;\n *   *only the lowercased first character is used, so `Right` is fine.*\n * @property {boolean | null | undefined} [delimiterEnd=true]\n *   Whether to end each row with the delimiter (default: `true`).\n *\n *   > 👉 **Note**: please don’t use this: it could create fragile structures\n *   > that aren’t understandable to some markdown parsers.\n *\n *   When `true`, there are ending delimiters:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there are no ending delimiters:\n *\n *   ```markdown\n *   | Alpha | B\n *   | ----- | -----\n *   | C     | Delta\n *   ```\n * @property {boolean | null | undefined} [delimiterStart=true]\n *   Whether to begin each row with the delimiter (default: `true`).\n *\n *   > 👉 **Note**: please don’t use this: it could create fragile structures\n *   > that aren’t understandable to some markdown parsers.\n *\n *   When `true`, there are starting delimiters:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there are no starting delimiters:\n *\n *   ```markdown\n *   Alpha | B     |\n *   ----- | ----- |\n *   C     | Delta |\n *   ```\n * @property {boolean | null | undefined} [padding=true]\n *   Whether to add a space of padding between delimiters and cells\n *   (default: `true`).\n *\n *   When `true`, there is padding:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there is no padding:\n *\n *   ```markdown\n *   |Alpha|B    |\n *   |-----|-----|\n *   |C    |Delta|\n *   ```\n * @property {((value: string) => number) | null | undefined} [stringLength]\n *   Function to detect the length of table cell content (optional);\n *   this is used when aligning the delimiters (`|`) between table cells;\n *   full-width characters and emoji mess up delimiter alignment when viewing\n *   the markdown source;\n *   to fix this, you can pass this function,\n *   which receives the cell content and returns its “visible” size;\n *   note that what is and isn’t visible depends on where the text is displayed.\n *\n *   Without such a function, the following:\n *\n *   ```js\n *   markdownTable([\n *     ['Alpha', 'Bravo'],\n *     ['中文', 'Charlie'],\n *     ['👩‍❤️‍👩', 'Delta']\n *   ])\n *   ```\n *\n *   Yields:\n *\n *   ```markdown\n *   | Alpha | Bravo |\n *   | - | - |\n *   | 中文 | Charlie |\n *   | 👩‍❤️‍👩 | Delta |\n *   ```\n *\n *   With [`string-width`](https://github.com/sindresorhus/string-width):\n *\n *   ```js\n *   import stringWidth from 'string-width'\n *\n *   markdownTable(\n *     [\n *       ['Alpha', 'Bravo'],\n *       ['中文', 'Charlie'],\n *       ['👩‍❤️‍👩', 'Delta']\n *     ],\n *     {stringLength: stringWidth}\n *   )\n *   ```\n *\n *   Yields:\n *\n *   ```markdown\n *   | Alpha | Bravo   |\n *   | ----- | ------- |\n *   | 中文  | Charlie |\n *   | 👩‍❤️‍👩    | Delta   |\n *   ```\n */\n\n/**\n * @param {string} value\n *   Cell value.\n * @returns {number}\n *   Cell size.\n */\nfunction defaultStringLength(value) {\n  return value.length\n}\n\n/**\n * Generate a markdown\n * ([GFM](https://docs.github.com/en/github/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables))\n * table.\n *\n * @param {ReadonlyArray<ReadonlyArray<string | null | undefined>>} table\n *   Table data (matrix of strings).\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Result.\n */\nfunction markdownTable(table, options) {\n  const settings = options || {}\n  // To do: next major: change to spread.\n  const align = (settings.align || []).concat()\n  const stringLength = settings.stringLength || defaultStringLength\n  /** @type {Array<number>} Character codes as symbols for alignment per column. */\n  const alignments = []\n  /** @type {Array<Array<string>>} Cells per row. */\n  const cellMatrix = []\n  /** @type {Array<Array<number>>} Sizes of each cell per row. */\n  const sizeMatrix = []\n  /** @type {Array<number>} */\n  const longestCellByColumn = []\n  let mostCellsPerRow = 0\n  let rowIndex = -1\n\n  // This is a superfluous loop if we don’t align delimiters, but otherwise we’d\n  // do superfluous work when aligning, so optimize for aligning.\n  while (++rowIndex < table.length) {\n    /** @type {Array<string>} */\n    const row = []\n    /** @type {Array<number>} */\n    const sizes = []\n    let columnIndex = -1\n\n    if (table[rowIndex].length > mostCellsPerRow) {\n      mostCellsPerRow = table[rowIndex].length\n    }\n\n    while (++columnIndex < table[rowIndex].length) {\n      const cell = serialize(table[rowIndex][columnIndex])\n\n      if (settings.alignDelimiters !== false) {\n        const size = stringLength(cell)\n        sizes[columnIndex] = size\n\n        if (\n          longestCellByColumn[columnIndex] === undefined ||\n          size > longestCellByColumn[columnIndex]\n        ) {\n          longestCellByColumn[columnIndex] = size\n        }\n      }\n\n      row.push(cell)\n    }\n\n    cellMatrix[rowIndex] = row\n    sizeMatrix[rowIndex] = sizes\n  }\n\n  // Figure out which alignments to use.\n  let columnIndex = -1\n\n  if (typeof align === 'object' && 'length' in align) {\n    while (++columnIndex < mostCellsPerRow) {\n      alignments[columnIndex] = toAlignment(align[columnIndex])\n    }\n  } else {\n    const code = toAlignment(align)\n\n    while (++columnIndex < mostCellsPerRow) {\n      alignments[columnIndex] = code\n    }\n  }\n\n  // Inject the alignment row.\n  columnIndex = -1\n  /** @type {Array<string>} */\n  const row = []\n  /** @type {Array<number>} */\n  const sizes = []\n\n  while (++columnIndex < mostCellsPerRow) {\n    const code = alignments[columnIndex]\n    let before = ''\n    let after = ''\n\n    if (code === 99 /* `c` */) {\n      before = ':'\n      after = ':'\n    } else if (code === 108 /* `l` */) {\n      before = ':'\n    } else if (code === 114 /* `r` */) {\n      after = ':'\n    }\n\n    // There *must* be at least one hyphen-minus in each alignment cell.\n    let size =\n      settings.alignDelimiters === false\n        ? 1\n        : Math.max(\n            1,\n            longestCellByColumn[columnIndex] - before.length - after.length\n          )\n\n    const cell = before + '-'.repeat(size) + after\n\n    if (settings.alignDelimiters !== false) {\n      size = before.length + size + after.length\n\n      if (size > longestCellByColumn[columnIndex]) {\n        longestCellByColumn[columnIndex] = size\n      }\n\n      sizes[columnIndex] = size\n    }\n\n    row[columnIndex] = cell\n  }\n\n  // Inject the alignment row.\n  cellMatrix.splice(1, 0, row)\n  sizeMatrix.splice(1, 0, sizes)\n\n  rowIndex = -1\n  /** @type {Array<string>} */\n  const lines = []\n\n  while (++rowIndex < cellMatrix.length) {\n    const row = cellMatrix[rowIndex]\n    const sizes = sizeMatrix[rowIndex]\n    columnIndex = -1\n    /** @type {Array<string>} */\n    const line = []\n\n    while (++columnIndex < mostCellsPerRow) {\n      const cell = row[columnIndex] || ''\n      let before = ''\n      let after = ''\n\n      if (settings.alignDelimiters !== false) {\n        const size =\n          longestCellByColumn[columnIndex] - (sizes[columnIndex] || 0)\n        const code = alignments[columnIndex]\n\n        if (code === 114 /* `r` */) {\n          before = ' '.repeat(size)\n        } else if (code === 99 /* `c` */) {\n          if (size % 2) {\n            before = ' '.repeat(size / 2 + 0.5)\n            after = ' '.repeat(size / 2 - 0.5)\n          } else {\n            before = ' '.repeat(size / 2)\n            after = before\n          }\n        } else {\n          after = ' '.repeat(size)\n        }\n      }\n\n      if (settings.delimiterStart !== false && !columnIndex) {\n        line.push('|')\n      }\n\n      if (\n        settings.padding !== false &&\n        // Don’t add the opening space if we’re not aligning and the cell is\n        // empty: there will be a closing space.\n        !(settings.alignDelimiters === false && cell === '') &&\n        (settings.delimiterStart !== false || columnIndex)\n      ) {\n        line.push(' ')\n      }\n\n      if (settings.alignDelimiters !== false) {\n        line.push(before)\n      }\n\n      line.push(cell)\n\n      if (settings.alignDelimiters !== false) {\n        line.push(after)\n      }\n\n      if (settings.padding !== false) {\n        line.push(' ')\n      }\n\n      if (\n        settings.delimiterEnd !== false ||\n        columnIndex !== mostCellsPerRow - 1\n      ) {\n        line.push('|')\n      }\n    }\n\n    lines.push(\n      settings.delimiterEnd === false\n        ? line.join('').replace(/ +$/, '')\n        : line.join('')\n    )\n  }\n\n  return lines.join('\\n')\n}\n\n/**\n * @param {string | null | undefined} [value]\n *   Value to serialize.\n * @returns {string}\n *   Result.\n */\nfunction serialize(value) {\n  return value === null || value === undefined ? '' : String(value)\n}\n\n/**\n * @param {string | null | undefined} value\n *   Value.\n * @returns {number}\n *   Alignment.\n */\nfunction toAlignment(value) {\n  const code = typeof value === 'string' ? value.codePointAt(0) : 0\n\n  return code === 67 /* `C` */ || code === 99 /* `c` */\n    ? 99 /* `c` */\n    : code === 76 /* `L` */ || code === 108 /* `l` */\n      ? 108 /* `l` */\n      : code === 82 /* `R` */ || code === 114 /* `r` */\n        ? 114 /* `r` */\n        : 0\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/markdown-table/index.js\n");

/***/ })

};
;