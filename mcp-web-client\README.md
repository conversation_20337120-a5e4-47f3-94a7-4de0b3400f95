# SAI MCP Client 🚀

Una aplicación web moderna y elegante para conectarse y gestionar servidores MCP (Model Context Protocol) con diseño profesional de Sistemas Administrativos Inteligentes.

## ✨ Características Principales

### 🎨 **Diseño Moderno SAI**
- **Logo SAI integrado** con branding corporativo
- **Modo claro/oscuro** con paleta de colores SAI personalizada
- **Diseño responsivo** optimizado para todas las pantallas
- **Animaciones suaves** y efectos visuales modernos
- **Interfaz estilo Discord** con navegación intuitiva

### 🌍 **Sistema Multiidioma**
- **Soporte completo** para Español e Inglés
- **Cambio dinámico** de idioma sin recargar
- **Persistencia** de preferencias de idioma
- **Traducciones completas** de toda la interfaz

### 💬 **Chat IA Avanzado**
- **Múltiples conversaciones** con gestión de pestañas
- **Persistencia local** de historial de chat
- **Integración con OpenAI** via webhook personalizado
- **Renderizado Markdown** con sintaxis highlighting
- **Gestión de herramientas MCP** en tiempo real

### 🔌 **Conectividad MCP**
- **Múltiples transportes**: STDIO, SSE y HTTP Streamable
- 🛠️ **Gestión de herramientas**: Ejecuta herramientas MCP con interfaz gráfica
- 📁 **Explorador de recursos**: Navega y lee recursos expuestos por servidores MCP
- 💬 **Sistema de prompts**: Genera y utiliza prompts predefinidos
- 📊 **Logs en tiempo real**: Monitorea la actividad de las conexiones
- 🔄 **Reconexión automática**: Manejo inteligente de conexiones perdidas

## Instalación y Uso

### Prerrequisitos

- Node.js 18+
- npm o yarn

### Configuración rápida

1. **Instalar dependencias**:
   ```bash
   npm install
   npm install express ws cors nodemon  # Para el servidor proxy
   ```

2. **Iniciar aplicación completa**:
   ```bash
   npm run dev:full
   ```
   - Aplicación web: `http://localhost:3000`
   - Servidor proxy: `http://localhost:3001`

### Scripts disponibles

- `npm run dev` - Solo aplicación web
- `npm run proxy:start` - Solo servidor proxy
- `npm run dev:full` - Aplicación web + proxy
- `npm run build` - Construir para producción

## Configuración de Conexiones

### STDIO (Procesos locales)

Para servidores MCP locales:

**Servidor de archivos**:
- Comando: `npx`
- Argumentos: `-y @modelcontextprotocol/server-filesystem /path/to/directory`

**Servidor de memoria**:
- Comando: `npx`
- Argumentos: `-y @modelcontextprotocol/server-memory`

### SSE/HTTP

Para servidores remotos:
- SSE: `http://localhost:3000/sse`
- HTTP Streamable: `http://localhost:3000/mcp`

## Arquitectura

- **Cliente MCP Base**: Maneja protocolo JSON-RPC y eventos
- **Transportes**: STDIO, SSE, HTTP Streamable
- **UI Components**: Dashboard, formularios, paneles
- **Servidor Proxy**: Permite STDIO desde navegador via WebSockets

## Desarrollo

Este proyecto usa Next.js con TypeScript y Tailwind CSS. Para agregar nuevos transportes o componentes, seguir los patrones existentes en `src/lib/mcp/` y `src/components/`.

## Licencia

MIT License
