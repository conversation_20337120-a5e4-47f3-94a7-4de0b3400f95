@import "tailwindcss";
@import "highlight.js/styles/github.css";

:root {
  /* Colores SAI - Modo Claro */
  --sai-primary: #0ea5e9;
  --sai-primary-dark: #0284c7;
  --sai-secondary: #f59e0b;
  --sai-secondary-dark: #d97706;
  --background: #ffffff;
  --foreground: #171717;
  --card: #ffffff;
  --card-foreground: #171717;
  --border: #e5e7eb;
  --input: #ffffff;
  --primary: #0ea5e9;
  --primary-foreground: #ffffff;
  --secondary: #f3f4f6;
  --secondary-foreground: #171717;
  --accent: #f3f4f6;
  --accent-foreground: #171717;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --muted: #f9fafb;
  --muted-foreground: #6b7280;
  --popover: #ffffff;
  --popover-foreground: #171717;
}

.dark {
  /* Colores SAI - Modo Oscuro */
  --background: #0a0a0a;
  --foreground: #ededed;
  --card: #1f2937;
  --card-foreground: #ededed;
  --border: #374151;
  --input: #1f2937;
  --primary: #0ea5e9;
  --primary-foreground: #ffffff;
  --secondary: #374151;
  --secondary-foreground: #ededed;
  --accent: #374151;
  --accent-foreground: #ededed;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --muted: #111827;
  --muted-foreground: #9ca3af;
  --popover: #1f2937;
  --popover-foreground: #ededed;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Animaciones suaves para transiciones de tema */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Estilos para scrollbars personalizados */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--muted-foreground);
}

/* Efectos de hover mejorados */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dark .hover-lift:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Animaciones de entrada */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Estilos para el logo SAI */
.sai-logo {
  filter: brightness(1);
  transition: filter 0.3s ease;
}

.dark .sai-logo {
  filter: brightness(1.2);
}

/* Animaciones adicionales */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Efectos de botones mejorados */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 hover-lift;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white font-medium py-2 px-4 rounded-lg transition-all duration-200;
}

.btn-ghost {
  @apply hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-all duration-200;
}

/* Efectos de tarjetas */
.card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 transition-all duration-200;
}

.card-hover {
  @apply card hover:shadow-md hover:-translate-y-1;
}

/* Efectos de inputs */
.input-modern {
  @apply border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
}

/* Gradientes SAI */
.gradient-sai {
  background: linear-gradient(135deg, #0ea5e9 0%, #f59e0b 100%);
}

.gradient-sai-subtle {
  background: linear-gradient(135deg, #f0f9ff 0%, #fffbeb 100%);
}

.dark .gradient-sai-subtle {
  background: linear-gradient(135deg, #0c4a6e 0%, #78350f 100%);
}

/* Efectos de texto */
.text-gradient {
  background: linear-gradient(135deg, #0ea5e9, #f59e0b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Mejoras de accesibilidad */
.focus-visible:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 ring-offset-white dark:ring-offset-gray-900;
}

/* Efectos de loading */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.dark .loading-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200px 100%;
}
