"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-text";
exports.ids = ["vendor-chunks/hast-util-to-text"];
exports.modules = {

/***/ "(ssr)/../node_modules/hast-util-to-text/lib/index.js":
/*!******************************************************!*\
  !*** ../node_modules/hast-util-to-text/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toText: () => (/* binding */ toText)\n/* harmony export */ });\n/* harmony import */ var unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-find-after */ \"(ssr)/../node_modules/unist-util-find-after/lib/index.js\");\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/../node_modules/hast-util-is-element/lib/index.js\");\n/**\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Nodes} Nodes\n * @typedef {import('hast').Parents} Parents\n * @typedef {import('hast').Text} Text\n * @typedef {import('hast-util-is-element').TestFunction} TestFunction\n */\n\n/**\n * @typedef {'normal' | 'nowrap' | 'pre' | 'pre-wrap'} Whitespace\n *   Valid and useful whitespace values (from CSS).\n *\n * @typedef {0 | 1 | 2} BreakNumber\n *   Specific break:\n *\n *   *   `0` — space\n *   *   `1` — line ending\n *   *   `2` — blank line\n *\n * @typedef {'\\n'} BreakForce\n *   Forced break.\n *\n * @typedef {boolean} BreakValue\n *   Whether there was a break.\n *\n * @typedef {BreakNumber | BreakValue | undefined} BreakBefore\n *   Any value for a break before.\n *\n * @typedef {BreakForce | BreakNumber | BreakValue | undefined} BreakAfter\n *   Any value for a break after.\n *\n * @typedef CollectionInfo\n *   Info on current collection.\n * @property {BreakAfter} breakAfter\n *   Whether there was a break after.\n * @property {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @property {Whitespace} whitespace\n *   Current whitespace setting.\n *\n * @typedef Options\n *   Configuration.\n * @property {Whitespace | null | undefined} [whitespace='normal']\n *   Initial CSS whitespace setting to use (default: `'normal'`).\n */\n\n\n\n\nconst searchLineFeeds = /\\n/g\nconst searchTabOrSpaces = /[\\t ]+/g\n\nconst br = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('br')\nconst cell = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)(isCell)\nconst p = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('p')\nconst row = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('tr')\n\n// Note that we don’t need to include void elements here as they don’t have text.\n// See: <https://github.com/wooorm/html-void-elements>\nconst notRendered = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  // List from: <https://html.spec.whatwg.org/multipage/rendering.html#hidden-elements>\n  'datalist',\n  'head',\n  'noembed',\n  'noframes',\n  'noscript', // Act as if we support scripting.\n  'rp',\n  'script',\n  'style',\n  'template',\n  'title',\n  // Hidden attribute.\n  hidden,\n  // From: <https://html.spec.whatwg.org/multipage/rendering.html#flow-content-3>\n  closedDialog\n])\n\n// See: <https://html.spec.whatwg.org/multipage/rendering.html#the-css-user-agent-style-sheet-and-presentational-hints>\nconst blockOrCaption = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'address', // Flow content\n  'article', // Sections and headings\n  'aside', // Sections and headings\n  'blockquote', // Flow content\n  'body', // Page\n  'caption', // `table-caption`\n  'center', // Flow content (legacy)\n  'dd', // Lists\n  'dialog', // Flow content\n  'dir', // Lists (legacy)\n  'dl', // Lists\n  'dt', // Lists\n  'div', // Flow content\n  'figure', // Flow content\n  'figcaption', // Flow content\n  'footer', // Flow content\n  'form,', // Flow content\n  'h1', // Sections and headings\n  'h2', // Sections and headings\n  'h3', // Sections and headings\n  'h4', // Sections and headings\n  'h5', // Sections and headings\n  'h6', // Sections and headings\n  'header', // Flow content\n  'hgroup', // Sections and headings\n  'hr', // Flow content\n  'html', // Page\n  'legend', // Flow content\n  'li', // Lists (as `display: list-item`)\n  'listing', // Flow content (legacy)\n  'main', // Flow content\n  'menu', // Lists\n  'nav', // Sections and headings\n  'ol', // Lists\n  'p', // Flow content\n  'plaintext', // Flow content (legacy)\n  'pre', // Flow content\n  'section', // Sections and headings\n  'ul', // Lists\n  'xmp' // Flow content (legacy)\n])\n\n/**\n * Get the plain-text value of a node.\n *\n * ###### Algorithm\n *\n * *   if `tree` is a comment, returns its `value`\n * *   if `tree` is a text, applies normal whitespace collapsing to its\n *     `value`, as defined by the CSS Text spec\n * *   if `tree` is a root or element, applies an algorithm similar to the\n *     `innerText` getter as defined by HTML\n *\n * ###### Notes\n *\n * > 👉 **Note**: the algorithm acts as if `tree` is being rendered, and as if\n * > we’re a CSS-supporting user agent, with scripting enabled.\n *\n * *   if `tree` is an element that is not displayed (such as a `head`), we’ll\n *     still use the `innerText` algorithm instead of switching to `textContent`\n * *   if descendants of `tree` are elements that are not displayed, they are\n *     ignored\n * *   CSS is not considered, except for the default user agent style sheet\n * *   a line feed is collapsed instead of ignored in cases where Fullwidth, Wide,\n *     or Halfwidth East Asian Width characters are used, the same goes for a case\n *     with Chinese, Japanese, or Yi writing systems\n * *   replaced elements (such as `audio`) are treated like non-replaced elements\n *\n * @param {Nodes} tree\n *   Tree to turn into text.\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `tree`.\n */\nfunction toText(tree, options) {\n  const options_ = options || {}\n  const children = 'children' in tree ? tree.children : []\n  const block = blockOrCaption(tree)\n  const whitespace = inferWhitespace(tree, {\n    whitespace: options_.whitespace || 'normal',\n    breakBefore: false,\n    breakAfter: false\n  })\n\n  /** @type {Array<BreakNumber | string>} */\n  const results = []\n\n  // Treat `text` and `comment` as having normal white-space.\n  // This deviates from the spec as in the DOM the node’s `.data` has to be\n  // returned.\n  // If you want that behavior use `hast-util-to-string`.\n  // All other nodes are later handled as if they are `element`s (so the\n  // algorithm also works on a `root`).\n  // Nodes without children are treated as a void element, so `doctype` is thus\n  // ignored.\n  if (tree.type === 'text' || tree.type === 'comment') {\n    results.push(\n      ...collectText(tree, {\n        whitespace,\n        breakBefore: true,\n        breakAfter: true\n      })\n    )\n  }\n\n  // 1.  If this element is not being rendered, or if the user agent is a\n  //     non-CSS user agent, then return the same value as the textContent IDL\n  //     attribute on this element.\n  //\n  //     Note: we’re not supporting stylesheets so we’re acting as if the node\n  //     is rendered.\n  //\n  //     If you want that behavior use `hast-util-to-string`.\n  //     Important: we’ll have to account for this later though.\n\n  // 2.  Let results be a new empty list.\n  let index = -1\n\n  // 3.  For each child node node of this element:\n  while (++index < children.length) {\n    // 3.1. Let current be the list resulting in running the inner text\n    //      collection steps with node.\n    //      Each item in results will either be a JavaScript string or a\n    //      positive integer (a required line break count).\n    // 3.2. For each item item in current, append item to results.\n    results.push(\n      ...renderedTextCollection(\n        children[index],\n        // @ts-expect-error: `tree` is a parent if we’re here.\n        tree,\n        {\n          whitespace,\n          breakBefore: index ? undefined : block,\n          breakAfter:\n            index < children.length - 1 ? br(children[index + 1]) : block\n        }\n      )\n    )\n  }\n\n  // 4.  Remove any items from results that are the empty string.\n  // 5.  Remove any runs of consecutive required line break count items at the\n  //     start or end of results.\n  // 6.  Replace each remaining run of consecutive required line break count\n  //     items with a string consisting of as many U+000A LINE FEED (LF)\n  //     characters as the maximum of the values in the required line break\n  //     count items.\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {number | undefined} */\n  let count\n\n  index = -1\n\n  while (++index < results.length) {\n    const value = results[index]\n\n    if (typeof value === 'number') {\n      if (count !== undefined && value > count) count = value\n    } else if (value) {\n      if (count !== undefined && count > -1) {\n        result.push('\\n'.repeat(count) || ' ')\n      }\n\n      count = -1\n      result.push(value)\n    }\n  }\n\n  // 7.  Return the concatenation of the string items in results.\n  return result.join('')\n}\n\n/**\n * <https://html.spec.whatwg.org/multipage/dom.html#rendered-text-collection-steps>\n *\n * @param {Nodes} node\n * @param {Parents} parent\n * @param {CollectionInfo} info\n * @returns {Array<BreakNumber | string>}\n */\nfunction renderedTextCollection(node, parent, info) {\n  if (node.type === 'element') {\n    return collectElement(node, parent, info)\n  }\n\n  if (node.type === 'text') {\n    return info.whitespace === 'normal'\n      ? collectText(node, info)\n      : collectPreText(node)\n  }\n\n  return []\n}\n\n/**\n * Collect an element.\n *\n * @param {Element} node\n *   Element node.\n * @param {Parents} parent\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<BreakNumber | string>}\n */\nfunction collectElement(node, parent, info) {\n  // First we infer the `white-space` property.\n  const whitespace = inferWhitespace(node, info)\n  const children = node.children || []\n  let index = -1\n  /** @type {Array<BreakNumber | string>} */\n  let items = []\n\n  // We’re ignoring point 3, and exiting without any content here, because we\n  // deviated from the spec in `toText` at step 3.\n  if (notRendered(node)) {\n    return items\n  }\n\n  /** @type {BreakNumber | undefined} */\n  let prefix\n  /** @type {BreakForce | BreakNumber | undefined} */\n  let suffix\n  // Note: we first detect if there is going to be a break before or after the\n  // contents, as that changes the white-space handling.\n\n  // 2.  If node’s computed value of `visibility` is not `visible`, then return\n  //     items.\n  //\n  //     Note: Ignored, as everything is visible by default user agent styles.\n\n  // 3.  If node is not being rendered, then return items. [...]\n  //\n  //     Note: We already did this above.\n\n  // See `collectText` for step 4.\n\n  // 5.  If node is a `<br>` element, then append a string containing a single\n  //     U+000A LINE FEED (LF) character to items.\n  if (br(node)) {\n    suffix = '\\n'\n  }\n\n  // 7.  If node’s computed value of `display` is `table-row`, and node’s CSS\n  //     box is not the last `table-row` box of the nearest ancestor `table`\n  //     box, then append a string containing a single U+000A LINE FEED (LF)\n  //     character to items.\n  //\n  //     See: <https://html.spec.whatwg.org/multipage/rendering.html#tables-2>\n  //     Note: needs further investigation as this does not account for implicit\n  //     rows.\n  else if (\n    row(node) &&\n    // @ts-expect-error: something up with types of parents.\n    (0,unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__.findAfter)(parent, node, row)\n  ) {\n    suffix = '\\n'\n  }\n\n  // 8.  If node is a `<p>` element, then append 2 (a required line break count)\n  //     at the beginning and end of items.\n  else if (p(node)) {\n    prefix = 2\n    suffix = 2\n  }\n\n  // 9.  If node’s used value of `display` is block-level or `table-caption`,\n  //     then append 1 (a required line break count) at the beginning and end of\n  //     items.\n  else if (blockOrCaption(node)) {\n    prefix = 1\n    suffix = 1\n  }\n\n  // 1.  Let items be the result of running the inner text collection steps with\n  //     each child node of node in tree order, and then concatenating the\n  //     results to a single list.\n  while (++index < children.length) {\n    items = items.concat(\n      renderedTextCollection(children[index], node, {\n        whitespace,\n        breakBefore: index ? undefined : prefix,\n        breakAfter:\n          index < children.length - 1 ? br(children[index + 1]) : suffix\n      })\n    )\n  }\n\n  // 6.  If node’s computed value of `display` is `table-cell`, and node’s CSS\n  //     box is not the last `table-cell` box of its enclosing `table-row` box,\n  //     then append a string containing a single U+0009 CHARACTER TABULATION\n  //     (tab) character to items.\n  //\n  //     See: <https://html.spec.whatwg.org/multipage/rendering.html#tables-2>\n  if (\n    cell(node) &&\n    // @ts-expect-error: something up with types of parents.\n    (0,unist_util_find_after__WEBPACK_IMPORTED_MODULE_1__.findAfter)(parent, node, cell)\n  ) {\n    items.push('\\t')\n  }\n\n  // Add the pre- and suffix.\n  if (prefix) items.unshift(prefix)\n  if (suffix) items.push(suffix)\n\n  return items\n}\n\n/**\n * 4.  If node is a Text node, then for each CSS text box produced by node,\n *     in content order, compute the text of the box after application of the\n *     CSS `white-space` processing rules and `text-transform` rules, set\n *     items to the list of the resulting strings, and return items.\n *     The CSS `white-space` processing rules are slightly modified:\n *     collapsible spaces at the end of lines are always collapsed, but they\n *     are only removed if the line is the last line of the block, or it ends\n *     with a br element.\n *     Soft hyphens should be preserved.\n *\n *     Note: See `collectText` and `collectPreText`.\n *     Note: we don’t deal with `text-transform`, no element has that by\n *     default.\n *\n * See: <https://drafts.csswg.org/css-text/#white-space-phase-1>\n *\n * @param {Comment | Text} node\n *   Text node.\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<BreakNumber | string>}\n *   Result.\n */\nfunction collectText(node, info) {\n  const value = String(node.value)\n  /** @type {Array<string>} */\n  const lines = []\n  /** @type {Array<BreakNumber | string>} */\n  const result = []\n  let start = 0\n\n  while (start <= value.length) {\n    searchLineFeeds.lastIndex = start\n\n    const match = searchLineFeeds.exec(value)\n    const end = match && 'index' in match ? match.index : value.length\n\n    lines.push(\n      // Any sequence of collapsible spaces and tabs immediately preceding or\n      // following a segment break is removed.\n      trimAndCollapseSpacesAndTabs(\n        // […] ignoring bidi formatting characters (characters with the\n        // Bidi_Control property [UAX9]: ALM, LTR, RTL, LRE-RLO, LRI-PDI) as if\n        // they were not there.\n        value\n          .slice(start, end)\n          .replace(/[\\u061C\\u200E\\u200F\\u202A-\\u202E\\u2066-\\u2069]/g, ''),\n        start === 0 ? info.breakBefore : true,\n        end === value.length ? info.breakAfter : true\n      )\n    )\n\n    start = end + 1\n  }\n\n  // Collapsible segment breaks are transformed for rendering according to the\n  // segment break transformation rules.\n  // So here we jump to 4.1.2 of [CSSTEXT]:\n  // Any collapsible segment break immediately following another collapsible\n  // segment break is removed\n  let index = -1\n  /** @type {BreakNumber | undefined} */\n  let join\n\n  while (++index < lines.length) {\n    // *   If the character immediately before or immediately after the segment\n    //     break is the zero-width space character (U+200B), then the break is\n    //     removed, leaving behind the zero-width space.\n    if (\n      lines[index].charCodeAt(lines[index].length - 1) === 0x20_0b /* ZWSP */ ||\n      (index < lines.length - 1 &&\n        lines[index + 1].charCodeAt(0) === 0x20_0b) /* ZWSP */\n    ) {\n      result.push(lines[index])\n      join = undefined\n    }\n\n    // *   Otherwise, if the East Asian Width property [UAX11] of both the\n    //     character before and after the segment break is Fullwidth, Wide, or\n    //     Halfwidth (not Ambiguous), and neither side is Hangul, then the\n    //     segment break is removed.\n    //\n    //     Note: ignored.\n    // *   Otherwise, if the writing system of the segment break is Chinese,\n    //     Japanese, or Yi, and the character before or after the segment break\n    //     is punctuation or a symbol (Unicode general category P* or S*) and\n    //     has an East Asian Width property of Ambiguous, and the character on\n    //     the other side of the segment break is Fullwidth, Wide, or Halfwidth,\n    //     and not Hangul, then the segment break is removed.\n    //\n    //     Note: ignored.\n\n    // *   Otherwise, the segment break is converted to a space (U+0020).\n    else if (lines[index]) {\n      if (typeof join === 'number') result.push(join)\n      result.push(lines[index])\n      join = 0\n    } else if (index === 0 || index === lines.length - 1) {\n      // If this line is empty, and it’s the first or last, add a space.\n      // Note that this function is only called in normal whitespace, so we\n      // don’t worry about `pre`.\n      result.push(0)\n    }\n  }\n\n  return result\n}\n\n/**\n * Collect a text node as “pre” whitespace.\n *\n * @param {Text} node\n *   Text node.\n * @returns {Array<BreakNumber | string>}\n *   Result.\n */\nfunction collectPreText(node) {\n  return [String(node.value)]\n}\n\n/**\n * 3.  Every collapsible tab is converted to a collapsible space (U+0020).\n * 4.  Any collapsible space immediately following another collapsible\n *     space—even one outside the boundary of the inline containing that\n *     space, provided both spaces are within the same inline formatting\n *     context—is collapsed to have zero advance width. (It is invisible,\n *     but retains its soft wrap opportunity, if any.)\n *\n * @param {string} value\n *   Value to collapse.\n * @param {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @param {BreakAfter} breakAfter\n *   Whether there was a break after.\n * @returns {string}\n *   Result.\n */\nfunction trimAndCollapseSpacesAndTabs(value, breakBefore, breakAfter) {\n  /** @type {Array<string>} */\n  const result = []\n  let start = 0\n  /** @type {number | undefined} */\n  let end\n\n  while (start < value.length) {\n    searchTabOrSpaces.lastIndex = start\n    const match = searchTabOrSpaces.exec(value)\n    end = match ? match.index : value.length\n\n    // If we’re not directly after a segment break, but there was white space,\n    // add an empty value that will be turned into a space.\n    if (!start && !end && match && !breakBefore) {\n      result.push('')\n    }\n\n    if (start !== end) {\n      result.push(value.slice(start, end))\n    }\n\n    start = match ? end + match[0].length : end\n  }\n\n  // If we reached the end, there was trailing white space, and there’s no\n  // segment break after this node, add an empty value that will be turned\n  // into a space.\n  if (start !== end && !breakAfter) {\n    result.push('')\n  }\n\n  return result.join(' ')\n}\n\n/**\n * Figure out the whitespace of a node.\n *\n * We don’t support void elements here (so `nobr wbr` -> `normal` is ignored).\n *\n * @param {Nodes} node\n *   Node (typically `Element`).\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Whitespace}\n *   Applied whitespace.\n */\nfunction inferWhitespace(node, info) {\n  if (node.type === 'element') {\n    const properties = node.properties || {}\n    switch (node.tagName) {\n      case 'listing':\n      case 'plaintext':\n      case 'xmp': {\n        return 'pre'\n      }\n\n      case 'nobr': {\n        return 'nowrap'\n      }\n\n      case 'pre': {\n        return properties.wrap ? 'pre-wrap' : 'pre'\n      }\n\n      case 'td':\n      case 'th': {\n        return properties.noWrap ? 'nowrap' : info.whitespace\n      }\n\n      case 'textarea': {\n        return 'pre-wrap'\n      }\n\n      default:\n    }\n  }\n\n  return info.whitespace\n}\n\n/**\n * @type {TestFunction}\n * @param {Element} node\n * @returns {node is {properties: {hidden: true}}}\n */\nfunction hidden(node) {\n  return Boolean((node.properties || {}).hidden)\n}\n\n/**\n * @type {TestFunction}\n * @param {Element} node\n * @returns {node is {tagName: 'td' | 'th'}}\n */\nfunction isCell(node) {\n  return node.tagName === 'td' || node.tagName === 'th'\n}\n\n/**\n * @type {TestFunction}\n */\nfunction closedDialog(node) {\n  return node.tagName === 'dialog' && !(node.properties || {}).open\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/hast-util-to-text/lib/index.js\n");

/***/ })

};
;