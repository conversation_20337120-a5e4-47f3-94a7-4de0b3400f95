// Prueba rápida para verificar el formato de IDs
const { v4: uuidv4 } = require('uuid');

function formatMessageId(id) {
  if (id.startsWith('msg_')) {
    return id;
  }
  return `msg_${id.replace(/-/g, '')}`;
}

function formatFunctionCallId(id) {
  if (id.startsWith('call_')) {
    return id;
  }
  return `call_${id.replace(/-/g, '')}`;
}

// Generar algunos IDs de prueba
console.log('IDs originales vs formateados:');
console.log('');

for (let i = 0; i < 3; i++) {
  const originalId = uuidv4();
  const messageId = formatMessageId(originalId);
  const callId = formatFunctionCallId(originalId);
  
  console.log(`Original: ${originalId}`);
  console.log(`Message:  ${messageId}`);
  console.log(`Call:     ${callId}`);
  console.log('');
}

// Verificar que cumple con los requisitos
const testId = uuidv4();
const formattedMsgId = formatMessageId(testId);
const formattedCallId = formatFunctionCallId(testId);

console.log('Verificación de formato:');
console.log(`Message ID starts with 'msg_': ${formattedMsgId.startsWith('msg_')}`);
console.log(`Call ID starts with 'call_': ${formattedCallId.startsWith('call_')}`);
console.log(`Message ID: ${formattedMsgId}`);
console.log(`Call ID: ${formattedCallId}`);
